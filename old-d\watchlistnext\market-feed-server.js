import "dotenv/config";
import WebSocket from "ws";
import express from "express";
import { createServer } from "http";
import { Server } from "socket.io";
import xlsx from "xlsx";
import multer from "multer";
import path from "path";
import fs from "fs";

// Dhan Market Feed Configuration
const MARKET_FEED_URL = "wss://api-feed.dhan.co";

// Exchange Segment Constants (based on official DhanHQ-py)
const EXCHANGE_SEGMENTS = {
  IDX_I: 0,
  NSE_EQ: 1,
  NSE_FNO: 2,
  NSE_CURRENCY: 3,
  BSE_EQ: 4,
  MCX_COMM: 5,
  BSE_CURRENCY: 7,
  BSE_FNO: 8,
};

// Subscription Type Constants
const SUBSCRIPTION_TYPES = {
  ticker: 15, // Basic LTP and LTT
  quote: 17, // Complete OHLC with volume and OI
  full: 21, // Complete data with market depth
};

class DhanMarketFeedServer {
  constructor() {
    this.accessToken = process.env.ACCESS_TOKEN?.trim();
    this.clientId = process.env.CLIENT_ID?.trim();
    this.subscriptionType = process.env.SUBSCRIPTION_TYPE?.trim() || "quote";
    this.instruments = this.parseInstruments(process.env.INSTRUMENTS);
    this.port = process.env.PORT || 3000;

    this.ws = null;
    this.isConnected = false;
    this.messageCount = 0;
    this.marketData = [];
    this.maxDataHistory = 100;

    // Store latest data for each instrument (single object per instrument)
    this.liveData = new Map();

    // Store previous close data for change calculation
    this.previousCloseData = new Map();

    // Express and Socket.IO setup
    this.app = express();
    this.server = createServer(this.app);
    this.io = new Server(this.server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"],
      },
    });

    // Setup multer for file uploads
    this.upload = multer({
      dest: "uploads/",
      fileFilter: (req, file, cb) => {
        const ext = path.extname(file.originalname).toLowerCase();
        if (ext === ".xlsx" || ext === ".xls") {
          cb(null, true);
        } else {
          cb(new Error("Only Excel files are allowed!"), false);
        }
      },
    });

    this.setupWebServer();
    this.loadInstrumentsFromExcel();
    this.validateConfig();
  }

  loadInstrumentsFromExcel() {
    try {
      const excelPath = "D:\\websocketdhan\\webdata.xlsx";

      // Check if file exists
      if (!fs.existsSync(excelPath)) {
        // If Excel file doesn't exist, use default instruments
        this.instruments = [
          { ticker: "HDFCBANK", exchange: "NSE_EQ", securityId: "1333" },
          { ticker: "RELIANCE", exchange: "NSE_EQ", securityId: "3045" },
        ];
        return;
      }

      // Read the Excel file
      const workbook = xlsx.readFile(excelPath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = xlsx.utils.sheet_to_json(worksheet);

      // Process the data and extract instruments
      const instruments = [];
      let validCount = 0;
      let invalidCount = 0;

      data.forEach((row, index) => {
        try {
          // Expected columns: Ticker, SecurityId, ExchangeSegment
          const ticker = row.Ticker || row.ticker || row.TICKER;
          const securityId =
            row.SecurityId ||
            row.securityId ||
            row.SecurityID ||
            row.SECURITYID;
          const exchangeSegment =
            row.ExchangeSegment ||
            row.exchangeSegment ||
            row.Exchange ||
            row.EXCHANGE;

          if (!securityId || !exchangeSegment) {
            invalidCount++;
            return; // Skip invalid rows
          }

          // Convert exchange segment to standard format
          let standardExchange = exchangeSegment;
          if (exchangeSegment === "1" || exchangeSegment === 1) {
            standardExchange = "NSE_EQ";
          } else if (exchangeSegment === "2" || exchangeSegment === 2) {
            standardExchange = "NSE_FNO";
          } else if (exchangeSegment === "3" || exchangeSegment === 3) {
            standardExchange = "NSE_CDS";
          } else if (exchangeSegment === "4" || exchangeSegment === 4) {
            standardExchange = "MCX_COMM";
          }

          // Validate exchange segment
          if (!EXCHANGE_SEGMENTS.hasOwnProperty(standardExchange)) {
            invalidCount++;
            if (index < 5) {
              // Log first few invalid exchanges for debugging
              console.log(
                `❌ Row ${
                  index + 1
                }: Invalid exchange - ${exchangeSegment} -> ${standardExchange}`
              );
            }
            return; // Skip invalid exchange
          }

          instruments.push({
            ticker: ticker || `${standardExchange}_${securityId}`,
            exchange: standardExchange,
            exchangeCode: EXCHANGE_SEGMENTS[standardExchange],
            securityId: securityId.toString(),
          });

          validCount++;
        } catch (error) {
          invalidCount++;
        }
      });

      this.instruments = instruments;

      if (this.instruments.length === 0) {
        this.instruments = [
          { ticker: "HDFCBANK", exchange: "NSE_EQ", securityId: "1333" },
          { ticker: "RELIANCE", exchange: "NSE_EQ", securityId: "3045" },
        ];
      }
    } catch (error) {
      // If Excel loading fails, use default instruments
      this.instruments = [
        { ticker: "HDFCBANK", exchange: "NSE_EQ", securityId: "1333" },
        { ticker: "RELIANCE", exchange: "NSE_EQ", securityId: "3045" },
      ];
    }
  }

  validateConfig() {
    if (!this.accessToken || !this.clientId) {
      throw new Error("ACCESS_TOKEN and CLIENT_ID must be set in .env file");
    }

    if (!SUBSCRIPTION_TYPES[this.subscriptionType]) {
      throw new Error(
        `Invalid SUBSCRIPTION_TYPE. Must be one of: ${Object.keys(
          SUBSCRIPTION_TYPES
        ).join(", ")}`
      );
    }

    if (!this.instruments || this.instruments.length === 0) {
      throw new Error(
        "No instruments loaded. Check Excel file or INSTRUMENTS in .env file"
      );
    }
  }

  parseInstruments(instrumentsStr) {
    if (!instrumentsStr) return [];

    return instrumentsStr.split(",").map((instrument) => {
      const [exchange, securityId] = instrument.trim().split(":");

      if (!exchange || !securityId) {
        throw new Error(
          `Invalid instrument format: ${instrument}. Use format: EXCHANGE:SECURITY_ID`
        );
      }

      const exchangeCode = EXCHANGE_SEGMENTS[exchange];
      if (exchangeCode === undefined) {
        throw new Error(
          `Invalid exchange: ${exchange}. Valid exchanges: ${Object.keys(
            EXCHANGE_SEGMENTS
          ).join(", ")}`
        );
      }

      return {
        exchange: exchange,
        exchangeCode: exchangeCode,
        securityId: securityId,
      };
    });
  }

  getExchangeSegmentName(code) {
    const reverseMap = Object.fromEntries(
      Object.entries(EXCHANGE_SEGMENTS).map(([key, value]) => [value, key])
    );
    return reverseMap[code] || `Unknown(${code})`;
  }

  setupWebServer() {
    // Serve static files
    this.app.use(express.static("public"));

    // API Routes
    this.app.get("/api/data", (req, res) => {
      res.json({
        connected: this.isConnected,
        messageCount: this.messageCount,
        instruments: this.instruments,
        subscriptionType: this.subscriptionType,
        latestData: this.marketData.slice(-20),
      });
    });

    this.app.get("/api/stats", (req, res) => {
      const stats = {
        connected: this.isConnected,
        totalMessages: this.messageCount,
        instrumentCount: this.instruments.length,
        subscriptionType: this.subscriptionType,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
      };
      res.json(stats);
    });

    // Live data endpoint - single object per instrument
    this.app.get("/api/live", (req, res) => {
      const liveData = {};

      // Convert live data map to clean JSON format
      this.liveData.forEach((item, key) => {
        const instrumentKey = `${item.exchangeName}_${item.securityId}`;
        liveData[instrumentKey] = this.formatLiveData(item);
      });

      res.json({
        timestamp: new Date().toISOString(),
        connected: this.isConnected,
        totalInstruments: this.liveData.size,
        totalMessages: this.messageCount,
        data: liveData,
      });
    });

    // Raw JSON data with change calculations
    this.app.get("/api/raw", (req, res) => {
      const rawData = [];

      // Convert live data map to raw JSON with change calculations
      this.liveData.forEach((item, key) => {
        // Get previous close data for this instrument
        const prevCloseData = this.previousCloseData.get(key);

        // Calculate change fields
        let priceChange = null;
        let percentageChange = null;

        if (item.ltp && prevCloseData && prevCloseData.previousClose) {
          priceChange = item.ltp - prevCloseData.previousClose;
          percentageChange = (priceChange / prevCloseData.previousClose) * 100;
        }

        // Find ticker for this instrument
        const instrument = this.instruments.find(
          (inst) =>
            inst.securityId == item.securityId &&
            inst.exchange === item.exchangeName
        );
        const ticker = instrument ? instrument.ticker : null;

        // Create raw data object with your exact structure + change fields
        const rawItem = {
          messageId: item.messageId,
          timestamp: item.timestamp,
          responseCode: item.responseCode,
          exchangeSegment: item.exchangeSegment,
          exchangeName: item.exchangeName,
          securityId: item.securityId,
          dataLength: item.dataLength,
          type: item.type,
          ticker: ticker,
          ltp: item.ltp || null,
          ltq: item.ltq || null,
          ltt: item.ltt || null,
          atp: item.atp || null,
          volume: item.volume || null,
          totalSellQuantity: item.totalSellQuantity || null,
          totalBuyQuantity: item.totalBuyQuantity || null,
          dayOpen: item.dayOpen || null,
          dayClose: item.dayClose || null,
          dayHigh: item.dayHigh || null,
          dayLow: item.dayLow || null,
          // Additional calculated fields
          priceChange: priceChange,
          percentageChange: percentageChange,
        };

        rawData.push(rawItem);
      });

      res.json({
        timestamp: new Date().toISOString(),
        connected: this.isConnected,
        totalInstruments: this.liveData.size,
        totalMessages: this.messageCount,
        changeCalculationMethod: {
          priceChange: "Current LTP - Previous Day Close Price",
          percentageChange: "(Price Change / Previous Day Close Price) * 100",
          note: "Previous Day Close Price comes from Response Code 6 packets sent by Dhan at market opening",
        },
        data: rawData,
      });
    });

    // All Excel instruments endpoint (shows all 1841 instruments)
    this.app.get("/api/excel", (req, res) => {
      const allInstruments = [];

      // Process all instruments from Excel
      this.instruments.forEach((instrument) => {
        const instrumentKey = `${EXCHANGE_SEGMENTS[instrument.exchange]}-${
          instrument.securityId
        }`;
        const liveData = this.liveData.get(instrumentKey);
        const prevCloseData = this.previousCloseData.get(instrumentKey);

        // Calculate change fields if live data exists
        let priceChange = null;
        let percentageChange = null;
        if (
          liveData &&
          liveData.ltp &&
          prevCloseData &&
          prevCloseData.previousClose
        ) {
          priceChange = liveData.ltp - prevCloseData.previousClose;
          percentageChange = (priceChange / prevCloseData.previousClose) * 100;
        }

        // Create instrument object
        const instrumentData = {
          ticker: instrument.ticker,
          securityId: parseInt(instrument.securityId),
          exchangeSegment: EXCHANGE_SEGMENTS[instrument.exchange],
          exchangeName: instrument.exchange,
          status: liveData ? "ACTIVE" : "SUBSCRIBED",
          // Market data (if available)
          messageId: liveData ? liveData.messageId : null,
          timestamp: liveData ? liveData.timestamp : null,
          responseCode: liveData ? liveData.responseCode : null,
          dataLength: liveData ? liveData.dataLength : null,
          type: liveData ? liveData.type : null,
          ltp: liveData ? liveData.ltp : null,
          ltq: liveData ? liveData.ltq : null,
          ltt: liveData ? liveData.ltt : null,
          atp: liveData ? liveData.atp : null,
          volume: liveData ? liveData.volume : null,
          totalSellQuantity: liveData ? liveData.totalSellQuantity : null,
          totalBuyQuantity: liveData ? liveData.totalBuyQuantity : null,
          dayOpen: liveData ? liveData.dayOpen : null,
          dayClose: liveData ? liveData.dayClose : null,
          dayHigh: liveData ? liveData.dayHigh : null,
          dayLow: liveData ? liveData.dayLow : null,
          openInterest: liveData ? liveData.openInterest : null,
          // Calculated fields
          priceChange: priceChange,
          percentageChange: percentageChange,
        };

        allInstruments.push(instrumentData);
      });

      res.json({
        timestamp: new Date().toISOString(),
        connected: this.isConnected,
        totalExcelInstruments: this.instruments.length,
        activeInstruments: this.liveData.size,
        totalMessages: this.messageCount,
        changeCalculationMethod: {
          priceChange: "Current LTP - Previous Day Close Price",
          percentageChange: "(Price Change / Previous Day Close Price) * 100",
          note: "Previous Day Close Price comes from Response Code 6 packets sent by Dhan at market opening",
        },
        data: allInstruments,
      });
    });

    // Excel upload route
    this.app.post(
      "/api/upload-excel",
      this.upload.single("excelFile"),
      (req, res) => {
        try {
          if (!req.file) {
            return res.status(400).json({ error: "No file uploaded" });
          }

          // Read the Excel file
          const workbook = xlsx.readFile(req.file.path);
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const data = xlsx.utils.sheet_to_json(worksheet);

          console.log(`📊 Processing Excel file with ${data.length} rows`);

          // Process the data and extract instruments
          const newInstruments = [];
          const errors = [];

          data.forEach((row, index) => {
            try {
              // Expected columns: Ticker, SecurityId, ExchangeSegment
              const ticker = row.Ticker || row.ticker;
              const securityId =
                row.SecurityId || row.securityId || row.SecurityID;
              const exchangeSegment =
                row.ExchangeSegment || row.exchangeSegment || row.Exchange;

              if (!securityId || !exchangeSegment) {
                errors.push(
                  `Row ${index + 1}: Missing SecurityId or ExchangeSegment`
                );
                return;
              }

              // Validate exchange segment
              if (!EXCHANGE_SEGMENTS.hasOwnProperty(exchangeSegment)) {
                errors.push(
                  `Row ${
                    index + 1
                  }: Invalid ExchangeSegment '${exchangeSegment}'. Valid values: ${Object.keys(
                    EXCHANGE_SEGMENTS
                  ).join(", ")}`
                );
                return;
              }

              newInstruments.push({
                ticker: ticker,
                exchange: exchangeSegment,
                exchangeCode: EXCHANGE_SEGMENTS[exchangeSegment],
                securityId: securityId.toString(),
              });
            } catch (error) {
              errors.push(`Row ${index + 1}: ${error.message}`);
            }
          });

          // Clean up uploaded file
          require("fs").unlinkSync(req.file.path);

          if (errors.length > 0) {
            console.log("⚠️ Excel processing errors:", errors);
          }

          console.log(
            `✅ Successfully processed ${newInstruments.length} instruments from Excel`
          );

          // Add new instruments to existing ones
          this.instruments = [...this.instruments, ...newInstruments];

          // Subscribe to new instruments if connected
          if (this.isConnected && newInstruments.length > 0) {
            this.subscribeToInstrumentList(newInstruments);
          }

          // Emit updated status to all clients
          this.io.emit("status", {
            connected: this.isConnected,
            messageCount: this.messageCount,
            instruments: this.instruments,
            subscriptionType: this.subscriptionType,
          });

          res.json({
            success: true,
            message: `Successfully processed ${newInstruments.length} instruments`,
            totalInstruments: this.instruments.length,
            newInstruments: newInstruments.length,
            errors: errors,
          });
        } catch (error) {
          console.error("🚨 Excel processing error:", error);
          res.status(500).json({ error: error.message });
        }
      }
    );

    // Test endpoint to simulate market data
    this.app.get("/api/test-market-data", (req, res) => {
      const testInstruments = ["100", "10074", "1008", "1333", "11536"]; // ARE&M, EMAMIPAP, FACT, HDFCBANK, TCS
      let updatedCount = 0;

      testInstruments.forEach((securityId) => {
        const instrumentKey = `1-${securityId}`; // NSE_EQ = 1
        const instrument = this.instruments.find(
          (inst) => inst.securityId === securityId
        );

        if (instrument) {
          // Simulate market data
          const basePrice = 100 + Math.random() * 500; // Random price between 100-600
          const previousClose = basePrice * (0.95 + Math.random() * 0.1); // Previous close within ±5%

          const marketData = {
            messageId: `TEST_${Date.now()}_${securityId}`,
            timestamp: new Date().toISOString(),
            responseCode: 4, // Quote packet
            exchangeSegment: 1,
            exchangeName: "NSE_EQ",
            securityId: parseInt(securityId),
            dataLength: 50,
            type: "quote",
            ticker: instrument.ticker,
            ltp: parseFloat(basePrice.toFixed(2)),
            ltq: Math.floor(Math.random() * 1000),
            ltt: Math.floor(Date.now() / 1000),
            atp: parseFloat(
              (basePrice * (0.99 + Math.random() * 0.02)).toFixed(2)
            ),
            volume: Math.floor(Math.random() * 100000),
            totalSellQuantity: Math.floor(Math.random() * 50000),
            totalBuyQuantity: Math.floor(Math.random() * 50000),
            dayOpen: parseFloat(
              (basePrice * (0.98 + Math.random() * 0.04)).toFixed(2)
            ),
            dayClose: parseFloat(basePrice.toFixed(2)),
            dayHigh: parseFloat(
              (basePrice * (1.01 + Math.random() * 0.05)).toFixed(2)
            ),
            dayLow: parseFloat(
              (basePrice * (0.95 + Math.random() * 0.03)).toFixed(2)
            ),
          };

          // Store in live data
          this.liveData.set(instrumentKey, marketData);

          // Store previous close for change calculation
          this.previousCloseData.set(instrumentKey, {
            previousClose: parseFloat(previousClose.toFixed(2)),
            previousOI: Math.floor(Math.random() * 10000),
          });

          updatedCount++;
        }
      });

      res.json({
        message: `Simulated market data for ${updatedCount} test instruments`,
        timestamp: new Date().toISOString(),
        testInstruments: testInstruments,
        note: "This is test data. Real market data will flow when market is open (9:15 AM - 3:30 PM IST)",
        instructions:
          "Check /api/excel endpoint to see the updated data with simulated prices",
      });
    });

    // Main dashboard route
    this.app.get("/", (req, res) => {
      res.send(this.getDashboardHTML());
    });

    // Simple dashboard route (without WebSocket)
    this.app.get("/simple", (req, res) => {
      res.sendFile(path.resolve("simple-dashboard.html"));
    });

    // Streaming dashboard route (auto-updating)
    this.app.get("/stream", (req, res) => {
      res.sendFile(path.resolve("streaming-dashboard.html"));
    });

    // Live table dashboard route (single object updates)
    this.app.get("/live", (req, res) => {
      res.sendFile(path.resolve("live-dashboard.html"));
    });

    // WebSocket live dashboard route (real-time single object updates)
    this.app.get("/ws", (req, res) => {
      res.sendFile(path.resolve("websocket-live-dashboard.html"));
    });

    // JSON WebSocket client route (pure JSON data)
    this.app.get("/json", (req, res) => {
      res.sendFile(path.resolve("json-websocket-client.html"));
    });

    // Raw WebSocket client route (exact data structure)
    this.app.get("/raw", (req, res) => {
      res.sendFile(path.resolve("raw-websocket-client.html"));
    });

    // Single object WebSocket client route (one object per instrument)
    this.app.get("/single", (req, res) => {
      res.sendFile(path.resolve("single-object-client.html"));
    });

    // Auto-update client route (API + WebSocket modes)
    this.app.get("/auto", (req, res) => {
      res.sendFile(path.resolve("auto-update-client.html"));
    });

    // Live Excel dashboard route (WebSocket auto-updates, no manual refresh)
    this.app.get("/excel", (req, res) => {
      res.sendFile(path.resolve("live-excel-dashboard.html"));
    });

    // Server-Sent Events raw data dashboard
    this.app.get("/sse", (req, res) => {
      res.sendFile(path.resolve("sse-raw-data.html"));
    });

    // Fixed position raw data dashboard (instruments stay in same rows)
    this.app.get("/fixed", (req, res) => {
      res.sendFile(path.resolve("fixed-position-raw-data.html"));
    });

    // Interactive market dashboard with advanced UI
    this.app.get("/interactive", (req, res) => {
      res.sendFile(path.resolve("interactive-market-dashboard.html"));
    });

    // Premium trading dashboard with comprehensive features
    this.app.get("/premium", (req, res) => {
      res.sendFile(path.resolve("premium-trading-dashboard.html"));
    });

    // Optimized trading dashboard - high performance
    this.app.get("/optimized", (req, res) => {
      res.sendFile(path.resolve("optimized-trading-dashboard.html"));
    });

    // Structured raw data viewer
    this.app.get("/structured", (req, res) => {
      res.sendFile(path.resolve("structured-raw-data.html"));
    });

    // Ultra-fast dashboard - maximum speed
    this.app.get("/ultrafast", (req, res) => {
      res.sendFile(path.resolve("ultra-fast-dashboard.html"));
    });

    // Live raw JSON WebSocket viewer
    this.app.get("/rawjson", (req, res) => {
      res.sendFile(path.resolve("live-raw-json-viewer.html"));
    });

    // Auto-updating raw data API endpoint (Server-Sent Events)
    this.app.get("/api/raw/stream", (req, res) => {
      // Set headers for Server-Sent Events
      res.writeHead(200, {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      });

      // Send initial connection message
      res.write(
        "data: " +
          JSON.stringify({
            type: "connection",
            message: "Connected to raw data stream",
            timestamp: new Date().toISOString(),
            connected: this.isConnected,
            totalInstruments: this.liveData.size,
          }) +
          "\n\n"
      );

      // Store client connection for raw data updates
      const clientId = Date.now();
      if (!this.sseClients) {
        this.sseClients = new Map();
      }
      this.sseClients.set(clientId, res);

      // Handle client disconnect
      req.on("close", () => {
        this.sseClients.delete(clientId);
      });
    });

    // Socket.IO connection handling
    this.io.on("connection", (socket) => {
      // Send current status to new client
      socket.emit("status", {
        connected: this.isConnected,
        messageCount: this.messageCount,
        instruments: this.instruments,
        subscriptionType: this.subscriptionType,
      });

      // Send recent data to new client
      socket.emit("history", this.marketData.slice(-10));

      socket.on("disconnect", () => {
        // Silent disconnect
      });

      socket.on("subscribe", (data) => {
        if (data.exchange && data.securityId) {
          this.subscribeToNewInstrument(
            data.exchange,
            data.securityId,
            data.type || "quote"
          );
        }
      });
    });
  }

  getDashboardHTML() {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dhan Market Feed - Live Dashboard</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            line-height: 1.6;
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .status {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .status-card {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
            flex: 1;
            margin: 5px;
            min-width: 200px;
        }
        .status-card.disconnected { border-left-color: #f44336; }
        .data-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .data-panel {
            background: #1a1a1a;
            border-radius: 10px;
            padding: 20px;
            height: 600px;
            overflow-y: auto;
        }
        .data-item {
            background: #2a2a2a;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
            transition: all 0.3s ease;
        }
        .data-item:hover { transform: translateX(5px); }
        .data-item.ticker { border-left-color: #FF9800; }
        .data-item.quote { border-left-color: #4CAF50; }
        .data-item.full { border-left-color: #9C27B0; }
        .data-item.previousClose { border-left-color: #F44336; }
        .data-item.openInterest { border-left-color: #00BCD4; }
        .data-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .data-type {
            background: #333;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            text-transform: uppercase;
        }
        .data-fields {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        .field { font-size: 0.9em; }
        .field-label { color: #888; font-size: 0.8em; }
        .field-value { font-weight: bold; color: #fff; }
        .raw-data {
            background: #0a0a0a;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.8em;
            margin-top: 10px;
            word-break: break-all;
        }
        .controls {
            background: #1a1a1a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .upload-section {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border-left: 4px solid #FF9800;
        }
        .file-input {
            margin: 10px 0;
        }
        .upload-status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .upload-status.success {
            background-color: #4CAF50;
            color: white;
        }
        .upload-status.error {
            background-color: #f44336;
            color: white;
        }
        .control-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }
        input, select, button {
            padding: 8px 12px;
            border: 1px solid #333;
            background: #2a2a2a;
            color: #fff;
            border-radius: 4px;
        }
        button {
            background: #4CAF50;
            cursor: pointer;
            border: none;
        }
        button:hover { background: #45a049; }
        .timestamp { color: #888; font-size: 0.8em; }
        @media (max-width: 768px) {
            .data-container { grid-template-columns: 1fr; }
            .status { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Dhan Market Feed - Live Dashboard</h1>
            <p>Real-time market data streaming via WebSocket</p>
        </div>

        <div class="status">
            <div class="status-card" id="connection-status">
                <h3>Connection Status</h3>
                <p id="status-text">Connecting...</p>
            </div>
            <div class="status-card">
                <h3>Messages Received</h3>
                <p id="message-count">0</p>
            </div>
            <div class="status-card">
                <h3>Subscription Type</h3>
                <p id="subscription-type">-</p>
            </div>
            <div class="status-card">
                <h3>Instruments</h3>
                <p id="instrument-count">0</p>
            </div>
        </div>

        <div class="controls">
            <h3>Subscribe to New Instrument</h3>
            <div class="control-group">
                <select id="exchange">
                    <option value="NSE_EQ">NSE_EQ</option>
                    <option value="NSE_FNO">NSE_FNO</option>
                    <option value="BSE_EQ">BSE_EQ</option>
                    <option value="MCX_COMM">MCX_COMM</option>
                </select>
                <input type="text" id="security-id" placeholder="Security ID (e.g., 1333)" />
                <select id="sub-type">
                    <option value="quote">Quote</option>
                    <option value="ticker">Ticker</option>
                    <option value="full">Full</option>
                </select>
                <button onclick="subscribeToInstrument()">Subscribe</button>
            </div>

            <div class="upload-section">
                <h4>📊 Bulk Upload from Excel</h4>
                <p>Upload an Excel file with columns: <strong>Ticker</strong>, <strong>SecurityId</strong>, <strong>ExchangeSegment</strong></p>
                <div class="file-input">
                    <input type="file" id="excel-file" accept=".xlsx,.xls" />
                    <button onclick="uploadExcel()">Upload & Subscribe</button>
                </div>
                <div id="upload-status" class="upload-status"></div>
            </div>
        </div>

        <div class="data-container">
            <div class="data-panel">
                <h3>📊 Live Market Data</h3>
                <div id="live-data"></div>
            </div>
            <div class="data-panel">
                <h3>🔍 Raw Data Stream</h3>
                <div id="raw-data"></div>
            </div>
        </div>
    </div>

    <script>
        const socket = io();
        let messageCount = 0;

        socket.on('connect', () => {
            console.log('Connected to server');
            updateConnectionStatus(true);
        });

        socket.on('disconnect', () => {
            console.log('Disconnected from server');
            updateConnectionStatus(false);
        });

        socket.on('status', (data) => {
            updateStatus(data);
        });

        socket.on('marketData', (data) => {
            messageCount++;
            updateMessageCount(messageCount);
            addMarketData(data);
            addRawData(data);
        });

        socket.on('history', (data) => {
            data.forEach(item => addMarketData(item));
        });

        function updateConnectionStatus(connected) {
            const statusCard = document.getElementById('connection-status');
            const statusText = document.getElementById('status-text');

            if (connected) {
                statusCard.classList.remove('disconnected');
                statusText.textContent = '✅ Connected';
            } else {
                statusCard.classList.add('disconnected');
                statusText.textContent = '❌ Disconnected';
            }
        }

        function updateStatus(data) {
            document.getElementById('subscription-type').textContent = data.subscriptionType;
            document.getElementById('instrument-count').textContent = data.instruments.length;
            updateMessageCount(data.messageCount);
        }

        function updateMessageCount(count) {
            document.getElementById('message-count').textContent = count;
        }

        function addMarketData(data) {
            const container = document.getElementById('live-data');
            const item = document.createElement('div');
            item.className = \`data-item \${data.type}\`;

            let fieldsHTML = '';
            Object.entries(data).forEach(([key, value]) => {
                if (key !== 'messageId' && key !== 'timestamp' && key !== 'rawHex' && key !== 'dataLength') {
                    if (typeof value === 'object' && value !== null) {
                        fieldsHTML += \`<div class="field"><span class="field-label">\${key}:</span><br><span class="field-value">\${JSON.stringify(value, null, 2)}</span></div>\`;
                    } else {
                        fieldsHTML += \`<div class="field"><span class="field-label">\${key}:</span><br><span class="field-value">\${value}</span></div>\`;
                    }
                }
            });

            item.innerHTML = \`
                <div class="data-header">
                    <span class="data-type">\${data.type}</span>
                    <span class="timestamp">\${new Date(data.timestamp).toLocaleTimeString()}</span>
                </div>
                <div class="data-fields">
                    \${fieldsHTML}
                </div>
            \`;

            container.insertBefore(item, container.firstChild);

            // Keep only last 20 items
            while (container.children.length > 20) {
                container.removeChild(container.lastChild);
            }
        }

        function addRawData(data) {
            const container = document.getElementById('raw-data');
            const item = document.createElement('div');
            item.className = 'data-item';

            item.innerHTML = \`
                <div class="data-header">
                    <span class="data-type">Raw #\${data.messageId}</span>
                    <span class="timestamp">\${new Date(data.timestamp).toLocaleTimeString()}</span>
                </div>
                <div class="raw-data">\${data.rawHex || 'No raw data'}</div>
            \`;

            container.insertBefore(item, container.firstChild);

            // Keep only last 15 items
            while (container.children.length > 15) {
                container.removeChild(container.lastChild);
            }
        }

        function subscribeToInstrument() {
            const exchange = document.getElementById('exchange').value;
            const securityId = document.getElementById('security-id').value;
            const type = document.getElementById('sub-type').value;

            if (!securityId) {
                alert('Please enter a Security ID');
                return;
            }

            socket.emit('subscribe', { exchange, securityId, type });
            document.getElementById('security-id').value = '';
        }

        async function uploadExcel() {
            const fileInput = document.getElementById('excel-file');
            const statusDiv = document.getElementById('upload-status');

            if (!fileInput.files[0]) {
                showUploadStatus('Please select an Excel file', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('excelFile', fileInput.files[0]);

            try {
                showUploadStatus('Uploading and processing Excel file...', 'info');

                const response = await fetch('/api/upload-excel', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    let message = \`✅ \${result.message}\`;
                    if (result.errors && result.errors.length > 0) {
                        message += \`\\n⚠️ \${result.errors.length} errors occurred\`;
                    }
                    showUploadStatus(message, 'success');
                    fileInput.value = ''; // Clear file input
                } else {
                    showUploadStatus(\`❌ Error: \${result.error}\`, 'error');
                }
            } catch (error) {
                showUploadStatus(\`❌ Upload failed: \${error.message}\`, 'error');
            }
        }

        function showUploadStatus(message, type) {
            const statusDiv = document.getElementById('upload-status');
            statusDiv.textContent = message;
            statusDiv.className = \`upload-status \${type}\`;
            statusDiv.style.display = 'block';

            // Hide after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }
    </script>
</body>
</html>
    `;
  }

  async connect() {
    try {
      // Construct WebSocket URL with authentication parameters (Dhan API v2)
      const wsUrl = `${MARKET_FEED_URL}?version=2&token=${this.accessToken}&clientId=${this.clientId}&authType=2`;

      console.log(
        `🔗 Connecting to WebSocket: ${wsUrl.replace(
          this.accessToken,
          "TOKEN_HIDDEN"
        )}`
      );
      this.ws = new WebSocket(wsUrl);

      this.ws.on("open", () => {
        console.log(`✅ WebSocket connected successfully`);
        this.isConnected = true;
        this.io.emit("status", {
          connected: this.isConnected,
          messageCount: this.messageCount,
          instruments: this.instruments,
          subscriptionType: this.subscriptionType,
        });

        // Subscribe to instruments directly (authentication is via URL)
        this.subscribeToInstruments();
      });

      this.ws.on("message", (data) => {
        this.handleMessage(data);
      });

      this.ws.on("close", (code, reason) => {
        console.log(`❌ WebSocket closed: Code ${code}, Reason: ${reason}`);
        this.isConnected = false;
        this.io.emit("status", { connected: false });
        this.reconnect();
      });

      this.ws.on("error", (error) => {
        console.log(`🚨 WebSocket error: ${error.message}`);
        this.isConnected = false;
        this.io.emit("status", { connected: false });
      });
    } catch (error) {
      console.log(`🚨 Connection error: ${error.message}`);
      setTimeout(() => this.reconnect(), 5000);
    }
  }

  async subscribeToInstruments() {
    if (!this.isConnected) return;

    const requestCode = SUBSCRIPTION_TYPES[this.subscriptionType];

    // Debug logging removed for clean operation

    // Prepare instrument list for subscription - FIXED: Use STRING values like working market-feed.js
    const instrumentList = this.instruments.map((instrument) => {
      return {
        ExchangeSegment: instrument.exchange, // ✅ Use STRING like working market-feed.js
        SecurityId: instrument.securityId, // ✅ Use STRING like working market-feed.js
      };
    });

    // Split into chunks of 100 instruments (Dhan API limit)
    const chunkSize = 100;

    for (let i = 0; i < instrumentList.length; i += chunkSize) {
      const chunk = instrumentList.slice(i, i + chunkSize);

      const subscriptionMessage = {
        RequestCode: requestCode,
        InstrumentCount: chunk.length,
        InstrumentList: chunk,
      };

      this.ws.send(JSON.stringify(subscriptionMessage));

      // Small delay between chunks to avoid overwhelming the server
      if (i + chunkSize < instrumentList.length) {
        await new Promise((resolve) => setTimeout(resolve, 200));
      }
    }

    // Auto-remove logs after 30 seconds
    setTimeout(() => {
      console.log = () => {};
    }, 30000);
  }

  handleMessage(data) {
    try {
      this.messageCount++;

      if (!(data instanceof Buffer)) {
        return;
      }

      // Parse binary message header (8 bytes)
      const responseCode = data.readUInt8(0);
      const messageLength = data.readUInt16LE(1);
      const exchangeSegment = data.readUInt8(3);
      const securityId = data.readUInt32LE(4);

      const exchangeName = this.getExchangeSegmentName(exchangeSegment);
      const timestamp = new Date().toISOString();
      const instrumentKey = `${exchangeSegment}-${securityId}`;

      // Find ticker for this instrument from Excel data
      const instrument = this.instruments.find(
        (inst) =>
          inst.securityId == securityId &&
          EXCHANGE_SEGMENTS[inst.exchange] == exchangeSegment
      );
      const ticker = instrument ? instrument.ticker : `UNKNOWN_${securityId}`;

      // Debug logging removed for clean operation

      let marketData = {
        timestamp: timestamp,
        responseCode: responseCode,
        exchangeSegment: exchangeSegment,
        exchangeName: exchangeName,
        securityId: securityId,
        dataLength: data.length,
        ticker: ticker,
      };

      // Parse different response types based on response code
      switch (responseCode) {
        case 2: // Ticker packet
          if (data.length >= 16) {
            marketData.type = "ticker";
            marketData.ltp = data.readFloatLE(8);
            marketData.ltt = data.readUInt32LE(12);
          }
          break;

        case 4: // Quote packet
          if (data.length >= 50) {
            marketData.type = "quote";
            marketData.ltp = data.readFloatLE(8);
            marketData.ltq = data.readUInt16LE(12);
            marketData.ltt = data.readUInt32LE(14);
            marketData.atp = data.readFloatLE(18);
            marketData.volume = data.readUInt32LE(22);
            marketData.totalSellQuantity = data.readUInt32LE(26);
            marketData.totalBuyQuantity = data.readUInt32LE(30);
            marketData.dayOpen = data.readFloatLE(34);
            marketData.dayClose = data.readFloatLE(38);
            marketData.dayHigh = data.readFloatLE(42);
            marketData.dayLow = data.readFloatLE(46);
          }
          break;

        case 5: // Open Interest packet
          if (data.length >= 12) {
            marketData.type = "openInterest";
            marketData.openInterest = data.readUInt32LE(8);
          }
          break;

        case 6: // Previous close packet
          // According to Dhan documentation:
          // Bytes 0-8: Response Header with code 6
          // Bytes 9-12: float32 - Previous day closing price
          // Bytes 13-16: int32 - Open Interest - previous day
          if (data.length >= 16) {
            marketData.type = "previousClose";
            marketData.previousClose = data.readFloatLE(8); // Bytes 9-12 (0-indexed: 8-11)
            marketData.previousOI = data.readUInt32LE(12); // Bytes 13-16 (0-indexed: 12-15)

            // Store previous close data for change calculation
            this.previousCloseData.set(instrumentKey, {
              previousClose: marketData.previousClose,
              previousOI: marketData.previousOI,
            });
          }
          break;

        case 8: // Full packet (with market depth)
          if (data.length >= 162) {
            marketData.type = "full";
            marketData.ltp = data.readFloatLE(8);
            marketData.ltq = data.readUInt16LE(12);
            marketData.ltt = data.readUInt32LE(14);
            marketData.atp = data.readFloatLE(18);
            marketData.volume = data.readUInt32LE(22);
            marketData.totalSellQuantity = data.readUInt32LE(26);
            marketData.totalBuyQuantity = data.readUInt32LE(30);
            marketData.openInterest = data.readUInt32LE(34);
            marketData.dayOpen = data.readFloatLE(46);
            marketData.dayClose = data.readFloatLE(50);
            marketData.dayHigh = data.readFloatLE(54);
            marketData.dayLow = data.readFloatLE(58);

            // Parse market depth (5 levels, 20 bytes each)
            marketData.marketDepth = [];
            for (let i = 0; i < 5; i++) {
              const offset = 62 + i * 20;
              marketData.marketDepth.push({
                bidQuantity: data.readUInt32LE(offset),
                askQuantity: data.readUInt32LE(offset + 4),
                bidOrders: data.readUInt16LE(offset + 8),
                askOrders: data.readUInt16LE(offset + 10),
                bidPrice: data.readFloatLE(offset + 12),
                askPrice: data.readFloatLE(offset + 16),
              });
            }
          }
          break;

        case 50: // Disconnection packet
          if (data.length >= 10) {
            marketData.type = "disconnection";
            marketData.disconnectionCode = data.readUInt16LE(8);
          }
          break;

        default:
          marketData.type = "unknown";
      }

      // Update live data map (single object per instrument) - OPTIMIZED
      this.liveData.set(instrumentKey, marketData);

      // Format data using formatLiveData and emit "liveDataUpdate"
      const formattedData = this.formatLiveData(marketData);
      this.io.emit("liveDataUpdate", formattedData);

      // OPTIONAL: Store minimal history (reduced from full history)
      if (this.marketData.length > 100) {
        // Reduced from maxDataHistory
        this.marketData = this.marketData.slice(-50); // Keep only last 50
      }
      this.marketData.push(marketData);
    } catch (error) {
      // Silent error handling
    }
  }

  formatLiveData(item) {
    // Calculate price change
    let change = null;
    let changePercent = null;
    if (item.ltp && item.dayClose && item.dayClose !== item.ltp) {
      change = item.ltp - item.dayClose;
      changePercent = (change / item.dayClose) * 100;
    }

    return {
      ticker: item.ticker,
      exchange: item.exchangeName,
      securityId: item.securityId,
      type: item.type,
      ltp: item.ltp || null,
      priceChange: change ? parseFloat(change.toFixed(2)) : null,
      percentageChange: changePercent
        ? parseFloat(changePercent.toFixed(2))
        : null,
      volume: item.volume || null,
      dayHigh: item.dayHigh || null,
      dayLow: item.dayLow || null,
      dayOpen: item.dayOpen || null,
      dayClose: item.dayClose || null,
      atp: item.atp || null,
      totalBuyQuantity: item.totalBuyQuantity || null,
      totalSellQuantity: item.totalSellQuantity || null,
      openInterest: item.openInterest || null,
      timestamp: item.timestamp,
    };
  }

  async subscribeToNewInstrument(
    exchange,
    securityId,
    subscriptionType = "quote"
  ) {
    if (!this.isConnected) {
      return;
    }

    const requestCode =
      SUBSCRIPTION_TYPES[subscriptionType] || SUBSCRIPTION_TYPES.quote;

    const subscriptionMessage = {
      RequestCode: requestCode,
      InstrumentCount: 1,
      InstrumentList: [
        {
          ExchangeSegment: exchange, // ✅ Use STRING like working market-feed.js
          SecurityId: securityId, // ✅ Use STRING like working market-feed.js
        },
      ],
    };

    this.ws.send(JSON.stringify(subscriptionMessage));
  }

  async subscribeToInstrumentList(instruments, subscriptionType = null) {
    if (!this.isConnected) {
      return;
    }

    const requestCode =
      SUBSCRIPTION_TYPES[subscriptionType || this.subscriptionType];

    // Prepare instrument list for subscription - FIXED: Use STRING values like working market-feed.js
    const instrumentList = instruments.map((instrument) => ({
      ExchangeSegment: instrument.exchange, // ✅ Use STRING like working market-feed.js
      SecurityId: instrument.securityId, // ✅ Use STRING like working market-feed.js
    }));

    const subscriptionMessage = {
      RequestCode: requestCode,
      InstrumentCount: instrumentList.length,
      InstrumentList: instrumentList,
    };

    this.ws.send(JSON.stringify(subscriptionMessage));
  }

  async reconnect() {
    if (this.isConnected) return;

    setTimeout(() => {
      this.connect();
    }, 5000);
  }

  async disconnect() {
    if (this.ws && this.isConnected) {
      // Send disconnect message
      const disconnectMessage = { RequestCode: 12 };
      this.ws.send(JSON.stringify(disconnectMessage));

      this.ws.close();
    }
  }

  async startServer() {
    this.server.listen(this.port, () => {
      console.log(`🚀 Server running on http://localhost:${this.port}`);
      console.log(`📊 Available dashboards:`);
      console.log(`   • http://localhost:${this.port}/ - Main dashboard`);
      console.log(`   • http://localhost:${this.port}/excel - Excel dashboard`);
      console.log(
        `   • http://localhost:${this.port}/fixed - Fixed position dashboard`
      );
      console.log(
        `   • http://localhost:${this.port}/interactive - Interactive dashboard`
      );
      console.log(
        `   • http://localhost:${this.port}/premium - 🌟 Premium trading dashboard`
      );
      console.log(
        `   • http://localhost:${this.port}/optimized - ⚡ Optimized high-performance dashboard`
      );
      console.log(
        `   • http://localhost:${this.port}/structured - 📋 Structured raw data viewer`
      );
      console.log(
        `   • http://localhost:${this.port}/ultrafast - 🚀 Ultra-fast maximum speed dashboard`
      );
      console.log(`   • http://localhost:${this.port}/raw - Raw data viewer`);
      console.log(`   • http://localhost:${this.port}/sse - SSE dashboard`);
    });

    // Start market feed connection
    await this.connect();
  }
}

// Main execution
async function main() {
  const marketFeedServer = new DhanMarketFeedServer();

  // Handle graceful shutdown
  process.on("SIGINT", async () => {
    await marketFeedServer.disconnect();
    process.exit(0);
  });

  process.on("SIGTERM", async () => {
    await marketFeedServer.disconnect();
    process.exit(0);
  });

  // Start the server
  await marketFeedServer.startServer();
}

// Start the application
main().catch((error) => {
  process.exit(1);
});
