"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWebSocketConnection = void 0;
const socket_io_client_1 = require("socket.io-client");
const createWebSocketConnection = (options = {}) => {
    const socketInstance = (0, socket_io_client_1.io)("http://localhost:8080", {
        transports: ["websocket", "polling"],
        upgrade: true,
        rememberUpgrade: true,
        timeout: 20000,
        forceNew: false,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
    });
    socketInstance.on("connect", () => {
        console.log("Connected to WebSocket server");
        options.onConnect?.();
    });
    socketInstance.on("disconnect", (reason) => {
        console.log("Disconnected from WebSocket server:", reason);
        options.onDisconnect?.(reason);
        if (reason === "io server disconnect") {
            socketInstance.connect();
        }
    });
    socketInstance.on("connect_error", (error) => {
        console.error("WebSocket connection error:", error);
        options.onError?.(error);
    });
    socketInstance.on("reconnect", (attemptNumber) => {
        console.log("Reconnected after", attemptNumber, "attempts");
        options.onReconnect?.(attemptNumber);
    });
    if (options.onMarketData) {
        socketInstance.on("marketData", options.onMarketData);
    }
    if (options.onMarketDataBatch) {
        socketInstance.on("marketDataBatch", options.onMarketDataBatch);
    }
    return socketInstance;
};
exports.createWebSocketConnection = createWebSocketConnection;
//# sourceMappingURL=websocket.js.map