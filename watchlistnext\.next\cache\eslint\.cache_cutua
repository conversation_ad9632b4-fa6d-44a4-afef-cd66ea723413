[{"D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\api\\data\\route.ts": "1", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\api\\indices\\route.ts": "2", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\api\\stock\\[ticker]\\route.ts": "3", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\layout.tsx": "4", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\page.tsx": "5", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\stock\\[ticker]\\page.tsx": "6", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\App.tsx": "7", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\ConnectionStatus.tsx": "8", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\Dashboard.tsx": "9", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\EnhancedMarketDataTable.tsx": "10", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\KeyIndicesComponent.tsx": "11", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\MarketData.tsx": "12", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\MarketDataTable.tsx": "13", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\Pagination.tsx": "14", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\Stats.tsx": "15", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\config\\index.ts": "16", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\index.tsx": "17", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\lib\\stats.ts": "18", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\middleware\\error.ts": "19", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\server\\index.ts": "20", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\types\\express.d.ts": "21", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\types\\index.ts": "22", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\types\\next.d.ts": "23", "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\utils\\logger.ts": "24"}, {"size": 187, "mtime": 1749620504871, "results": "25", "hashOfConfig": "26"}, {"size": 3291, "mtime": 1749620504871, "results": "27", "hashOfConfig": "26"}, {"size": 3932, "mtime": 1749620504872, "results": "28", "hashOfConfig": "26"}, {"size": 430, "mtime": 1749620504872, "results": "29", "hashOfConfig": "26"}, {"size": 11652, "mtime": 1749620504873, "results": "30", "hashOfConfig": "26"}, {"size": 32582, "mtime": 1749707125094, "results": "31", "hashOfConfig": "26"}, {"size": 215, "mtime": 1749620504871, "results": "32", "hashOfConfig": "26"}, {"size": 557, "mtime": 1749620504873, "results": "33", "hashOfConfig": "26"}, {"size": 6001, "mtime": 1749620504874, "results": "34", "hashOfConfig": "26"}, {"size": 7317, "mtime": 1749620504874, "results": "35", "hashOfConfig": "26"}, {"size": 8066, "mtime": 1749620504874, "results": "36", "hashOfConfig": "26"}, {"size": 2096, "mtime": 1749620504874, "results": "37", "hashOfConfig": "26"}, {"size": 6027, "mtime": 1749620504874, "results": "38", "hashOfConfig": "26"}, {"size": 5148, "mtime": 1749620504875, "results": "39", "hashOfConfig": "26"}, {"size": 1321, "mtime": 1749620504875, "results": "40", "hashOfConfig": "26"}, {"size": 613, "mtime": 1749620504875, "results": "41", "hashOfConfig": "26"}, {"size": 380, "mtime": 1749620504875, "results": "42", "hashOfConfig": "26"}, {"size": 406, "mtime": 1749620504875, "results": "43", "hashOfConfig": "26"}, {"size": 702, "mtime": 1749620504876, "results": "44", "hashOfConfig": "26"}, {"size": 30908, "mtime": 1749620504876, "results": "45", "hashOfConfig": "26"}, {"size": 392, "mtime": 1749620504877, "results": "46", "hashOfConfig": "26"}, {"size": 681, "mtime": 1749620504877, "results": "47", "hashOfConfig": "26"}, {"size": 224, "mtime": 1749620504877, "results": "48", "hashOfConfig": "26"}, {"size": 506, "mtime": 1749620504877, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18rbq4o", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\api\\data\\route.ts", [], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\api\\indices\\route.ts", [], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\api\\stock\\[ticker]\\route.ts", ["122"], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\layout.tsx", [], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\page.tsx", ["123", "124", "125", "126", "127"], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\stock\\[ticker]\\page.tsx", ["128", "129", "130", "131", "132", "133"], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\App.tsx", [], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\ConnectionStatus.tsx", [], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\Dashboard.tsx", ["134", "135", "136"], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\EnhancedMarketDataTable.tsx", [], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\KeyIndicesComponent.tsx", ["137", "138"], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\MarketData.tsx", [], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\MarketDataTable.tsx", [], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\Pagination.tsx", [], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\components\\Stats.tsx", [], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\config\\index.ts", [], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\index.tsx", [], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\lib\\stats.ts", [], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\middleware\\error.ts", ["139"], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\server\\index.ts", ["140", "141", "142", "143", "144", "145", "146", "147"], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\types\\express.d.ts", ["148"], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\types\\index.ts", [], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\types\\next.d.ts", ["149"], [], "D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\utils\\logger.ts", ["150", "151", "152", "153"], [], {"ruleId": "154", "severity": 1, "message": "155", "line": 27, "column": 14, "nodeType": "156", "messageId": "157", "endLine": 27, "endColumn": 17, "suggestions": "158"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 40, "column": 10, "nodeType": null, "messageId": "161", "endLine": 40, "endColumn": 16}, {"ruleId": "154", "severity": 1, "message": "155", "line": 40, "column": 40, "nodeType": "156", "messageId": "157", "endLine": 40, "endColumn": 43, "suggestions": "162"}, {"ruleId": "159", "severity": 1, "message": "163", "line": 63, "column": 9, "nodeType": null, "messageId": "161", "endLine": 63, "endColumn": 30}, {"ruleId": "154", "severity": 1, "message": "155", "line": 207, "column": 29, "nodeType": "156", "messageId": "157", "endLine": 207, "endColumn": 32, "suggestions": "164"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 266, "column": 6, "nodeType": "167", "endLine": 266, "endColumn": 8, "suggestions": "168"}, {"ruleId": "159", "severity": 1, "message": "169", "line": 3, "column": 38, "nodeType": null, "messageId": "161", "endLine": 3, "endColumn": 44}, {"ruleId": "159", "severity": 1, "message": "170", "line": 37, "column": 9, "nodeType": null, "messageId": "161", "endLine": 37, "endColumn": 15}, {"ruleId": "159", "severity": 1, "message": "160", "line": 43, "column": 10, "nodeType": null, "messageId": "161", "endLine": 43, "endColumn": 16}, {"ruleId": "154", "severity": 1, "message": "155", "line": 105, "column": 44, "nodeType": "156", "messageId": "157", "endLine": 105, "endColumn": 47, "suggestions": "171"}, {"ruleId": "154", "severity": 1, "message": "155", "line": 112, "column": 50, "nodeType": "156", "messageId": "157", "endLine": 112, "endColumn": 53, "suggestions": "172"}, {"ruleId": "154", "severity": 1, "message": "155", "line": 126, "column": 37, "nodeType": "156", "messageId": "157", "endLine": 126, "endColumn": 40, "suggestions": "173"}, {"ruleId": "159", "severity": 1, "message": "174", "line": 3, "column": 40, "nodeType": null, "messageId": "161", "endLine": 3, "endColumn": 56}, {"ruleId": "154", "severity": 1, "message": "155", "line": 57, "column": 47, "nodeType": "156", "messageId": "157", "endLine": 57, "endColumn": 50, "suggestions": "175"}, {"ruleId": "165", "severity": 1, "message": "176", "line": 116, "column": 6, "nodeType": "167", "endLine": 116, "endColumn": 23, "suggestions": "177"}, {"ruleId": "154", "severity": 1, "message": "155", "line": 51, "column": 41, "nodeType": "156", "messageId": "157", "endLine": 51, "endColumn": 44, "suggestions": "178"}, {"ruleId": "154", "severity": 1, "message": "155", "line": 87, "column": 45, "nodeType": "156", "messageId": "157", "endLine": 87, "endColumn": 48, "suggestions": "179"}, {"ruleId": "159", "severity": 1, "message": "180", "line": 11, "column": 3, "nodeType": null, "messageId": "161", "endLine": 11, "endColumn": 7}, {"ruleId": "159", "severity": 1, "message": "181", "line": 4, "column": 10, "nodeType": null, "messageId": "161", "endLine": 4, "endColumn": 22}, {"ruleId": "154", "severity": 1, "message": "155", "line": 78, "column": 19, "nodeType": "156", "messageId": "157", "endLine": 78, "endColumn": 22, "suggestions": "182"}, {"ruleId": "154", "severity": 1, "message": "155", "line": 181, "column": 26, "nodeType": "156", "messageId": "157", "endLine": 181, "endColumn": 29, "suggestions": "183"}, {"ruleId": "159", "severity": 1, "message": "184", "line": 181, "column": 31, "nodeType": null, "messageId": "161", "endLine": 181, "endColumn": 36}, {"ruleId": "159", "severity": 1, "message": "185", "line": 227, "column": 18, "nodeType": null, "messageId": "161", "endLine": 227, "endColumn": 23}, {"ruleId": "159", "severity": 1, "message": "186", "line": 501, "column": 20, "nodeType": null, "messageId": "161", "endLine": 501, "endColumn": 29}, {"ruleId": "187", "severity": 1, "message": "188", "line": 739, "column": 11, "nodeType": "189", "messageId": "190", "endLine": 739, "endColumn": 26, "fix": "191"}, {"ruleId": "154", "severity": 1, "message": "155", "line": 739, "column": 23, "nodeType": "156", "messageId": "157", "endLine": 739, "endColumn": 26, "suggestions": "192"}, {"ruleId": "159", "severity": 1, "message": "193", "line": 1, "column": 10, "nodeType": null, "messageId": "161", "endLine": 1, "endColumn": 17}, {"ruleId": "154", "severity": 1, "message": "155", "line": 10, "column": 23, "nodeType": "156", "messageId": "157", "endLine": 10, "endColumn": 26, "suggestions": "194"}, {"ruleId": "154", "severity": 1, "message": "155", "line": 2, "column": 36, "nodeType": "156", "messageId": "157", "endLine": 2, "endColumn": 39, "suggestions": "195"}, {"ruleId": "154", "severity": 1, "message": "155", "line": 5, "column": 37, "nodeType": "156", "messageId": "157", "endLine": 5, "endColumn": 40, "suggestions": "196"}, {"ruleId": "154", "severity": 1, "message": "155", "line": 8, "column": 36, "nodeType": "156", "messageId": "157", "endLine": 8, "endColumn": 39, "suggestions": "197"}, {"ruleId": "154", "severity": 1, "message": "155", "line": 11, "column": 37, "nodeType": "156", "messageId": "157", "endLine": 11, "endColumn": 40, "suggestions": "198"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["199", "200"], "@typescript-eslint/no-unused-vars", "'socket' is assigned a value but never used.", "unusedVar", ["201", "202"], "'batchUpdateTimeoutRef' is assigned a value but never used.", ["203", "204"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'updateMarketDataBatch' and 'updateMarketDataSingle'. Either include them or remove the dependency array.", "ArrayExpression", ["205"], "'useRef' is defined but never used.", "'router' is assigned a value but never used.", ["206", "207"], ["208", "209"], ["210", "211"], "'WebSocketMessage' is defined but never used.", ["212", "213"], "React Hook useEffect has a missing dependency: 'socket'. Either include it or remove the dependency array.", ["214"], ["215", "216"], ["217", "218"], "'next' is defined but never used.", "'readFileSync' is defined but never used.", ["219", "220"], ["221", "222"], "'index' is defined but never used.", "'error' is defined but never used.", "'jsonError' is defined but never used.", "prefer-const", "'marketData' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "223", "text": "224"}, ["225", "226"], "'Request' is defined but never used.", ["227", "228"], ["229", "230"], ["231", "232"], ["233", "234"], ["235", "236"], {"messageId": "237", "fix": "238", "desc": "239"}, {"messageId": "240", "fix": "241", "desc": "242"}, {"messageId": "237", "fix": "243", "desc": "239"}, {"messageId": "240", "fix": "244", "desc": "242"}, {"messageId": "237", "fix": "245", "desc": "239"}, {"messageId": "240", "fix": "246", "desc": "242"}, {"desc": "247", "fix": "248"}, {"messageId": "237", "fix": "249", "desc": "239"}, {"messageId": "240", "fix": "250", "desc": "242"}, {"messageId": "237", "fix": "251", "desc": "239"}, {"messageId": "240", "fix": "252", "desc": "242"}, {"messageId": "237", "fix": "253", "desc": "239"}, {"messageId": "240", "fix": "254", "desc": "242"}, {"messageId": "237", "fix": "255", "desc": "239"}, {"messageId": "240", "fix": "256", "desc": "242"}, {"desc": "257", "fix": "258"}, {"messageId": "237", "fix": "259", "desc": "239"}, {"messageId": "240", "fix": "260", "desc": "242"}, {"messageId": "237", "fix": "261", "desc": "239"}, {"messageId": "240", "fix": "262", "desc": "242"}, {"messageId": "237", "fix": "263", "desc": "239"}, {"messageId": "240", "fix": "264", "desc": "242"}, {"messageId": "237", "fix": "265", "desc": "239"}, {"messageId": "240", "fix": "266", "desc": "242"}, [23133, 23417], "const marketData: any = {\r\n        timestamp: timestamp,\r\n        responseCode: responseCode,\r\n        exchangeSegment: exchangeSegment,\r\n        exchangeName: exchangeName,\r\n        securityId: securityId.toString(),\r\n        dataLength: data.length,\r\n        ticker: ticker,\r\n      };", {"messageId": "237", "fix": "267", "desc": "239"}, {"messageId": "240", "fix": "268", "desc": "242"}, {"messageId": "237", "fix": "269", "desc": "239"}, {"messageId": "240", "fix": "270", "desc": "242"}, {"messageId": "237", "fix": "271", "desc": "239"}, {"messageId": "240", "fix": "272", "desc": "242"}, {"messageId": "237", "fix": "273", "desc": "239"}, {"messageId": "240", "fix": "274", "desc": "242"}, {"messageId": "237", "fix": "275", "desc": "239"}, {"messageId": "240", "fix": "276", "desc": "242"}, {"messageId": "237", "fix": "277", "desc": "239"}, {"messageId": "240", "fix": "278", "desc": "242"}, "suggestUnknown", {"range": "279", "text": "280"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "281", "text": "282"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "283", "text": "280"}, {"range": "284", "text": "282"}, {"range": "285", "text": "280"}, {"range": "286", "text": "282"}, "Update the dependencies array to be: [updateMarketDataBatch, updateMarketDataSingle]", {"range": "287", "text": "288"}, {"range": "289", "text": "280"}, {"range": "290", "text": "282"}, {"range": "291", "text": "280"}, {"range": "292", "text": "282"}, {"range": "293", "text": "280"}, {"range": "294", "text": "282"}, {"range": "295", "text": "280"}, {"range": "296", "text": "282"}, "Update the dependencies array to be: [connectSocketIO, socket]", {"range": "297", "text": "298"}, {"range": "299", "text": "280"}, {"range": "300", "text": "282"}, {"range": "301", "text": "280"}, {"range": "302", "text": "282"}, {"range": "303", "text": "280"}, {"range": "304", "text": "282"}, {"range": "305", "text": "280"}, {"range": "306", "text": "282"}, {"range": "307", "text": "280"}, {"range": "308", "text": "282"}, {"range": "309", "text": "280"}, {"range": "310", "text": "282"}, {"range": "311", "text": "280"}, {"range": "312", "text": "282"}, {"range": "313", "text": "280"}, {"range": "314", "text": "282"}, {"range": "315", "text": "280"}, {"range": "316", "text": "282"}, {"range": "317", "text": "280"}, {"range": "318", "text": "282"}, [759, 762], "unknown", [759, 762], "never", [873, 876], [873, 876], [6205, 6208], [6205, 6208], [7991, 7993], "[updateMarketDataBatch, updateMarketDataSingle]", [2730, 2733], [2730, 2733], [2920, 2923], [2920, 2923], [3258, 3261], [3258, 3261], [2293, 2296], [2293, 2296], [4796, 4813], "[connectSocketIO, socket]", [1353, 1356], [1353, 1356], [2375, 2378], [2375, 2378], [1808, 1811], [1808, 1811], [4916, 4919], [4916, 4919], [23149, 23152], [23149, 23152], [199, 202], [199, 202], [60, 63], [60, 63], [163, 166], [163, 166], [268, 271], [268, 271], [372, 375], [372, 375]]