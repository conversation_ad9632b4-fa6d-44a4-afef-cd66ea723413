const xlsx = require("xlsx");
const { Pool } = require("pg");
const path = require("path");

// Database configuration from your .env
const pool = new Pool({
  connectionString:
    "postgresql://postgres.fjognbnryybyeepcoukr:<EMAIL>:5432/postgres",
  ssl: {
    rejectUnauthorized: false,
  },
  connectionTimeoutMillis: 10000,
  idleTimeoutMillis: 30000,
  max: 10,
});

async function createCompanyListTable() {
  const createTableQuery = `
    CREATE TABLE IF NOT EXISTS company_list (
      id SERIAL PRIMARY KEY,
      company_name VARCHAR(500) NOT NULL,
      isin_no VARCHAR(20),
      instrument VARCHAR(50),
      sector_name VARCHAR(200),
      industry_new_name VARCHAR(200),
      sub_sector VARCHAR(200),
      micro_category VARCHAR(200),
      bse_security_id VARCHAR(20),
      nse_security_id VARCHAR(20),
      symbol VARCHAR(50),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `;

  try {
    await pool.query(createTableQuery);
    console.log("✅ Company list table created successfully");
  } catch (error) {
    console.error("❌ Error creating table:", error.message);
    throw error;
  }
}

async function insertCompanyData(companies) {
  const insertQuery = `
    INSERT INTO company_list (
      company_name, isin_no, instrument, sector_name, industry_new_name,
      sub_sector, micro_category, bse_security_id, nse_security_id, symbol
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
    ON CONFLICT DO NOTHING;
  `;

  let successCount = 0;
  let errorCount = 0;

  for (const company of companies) {
    try {
      // Handle NSE_SECURITY_ID that might be "-" or empty
      const nseSecurityId =
        company.NSE_SECURITY_ID === "-" || !company.NSE_SECURITY_ID
          ? null
          : company.NSE_SECURITY_ID.toString();
      const bseSecurityId = company.BSE_SECURITY_ID
        ? company.BSE_SECURITY_ID.toString()
        : null;

      await pool.query(insertQuery, [
        company.COMPANY_NAME || "",
        company["ISIN No"] || "",
        company.Instrument || "",
        company["Sector Name"] || "",
        company["Industry New Name"] || "",
        company["Sub-Sector"] || "",
        company["Micro Category"] || "",
        bseSecurityId,
        nseSecurityId,
        company.SYMBOL || "",
      ]);
      successCount++;
    } catch (error) {
      console.error(
        `❌ Error inserting company ${company.COMPANY_NAME}:`,
        error.message
      );
      errorCount++;
    }
  }

  console.log(`✅ Successfully inserted: ${successCount} companies`);
  console.log(`❌ Errors: ${errorCount} companies`);
}

async function main() {
  try {
    console.log("🚀 Starting company list import process...");

    // Read Excel file
    const filePath = path.join(__dirname, "Companylist.xlsx");
    console.log("📖 Reading Excel file:", filePath);

    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const companies = xlsx.utils.sheet_to_json(worksheet);

    console.log(`📊 Found ${companies.length} companies in Excel file`);

    // Create table
    await createCompanyListTable();

    // Clear existing data (optional)
    console.log("🗑️ Clearing existing data...");
    await pool.query("DELETE FROM company_list");

    // Insert data
    console.log("💾 Inserting company data...");
    await insertCompanyData(companies);

    // Show some statistics
    const countResult = await pool.query("SELECT COUNT(*) FROM company_list");
    console.log(`📈 Total companies in database: ${countResult.rows[0].count}`);

    // Show sample data
    const sampleResult = await pool.query("SELECT * FROM company_list LIMIT 5");
    console.log("\n📋 Sample data from database:");
    console.table(sampleResult.rows);

    console.log("✅ Import process completed successfully!");
  } catch (error) {
    console.error("❌ Import process failed:", error.message);
  } finally {
    await pool.end();
  }
}

main();
