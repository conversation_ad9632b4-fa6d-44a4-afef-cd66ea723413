<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ Ultra-Fast Dashboard</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body { 
            font-family: 'Segoe UI', sans-serif;
            background: #0a0a0f;
            color: #ffffff;
            line-height: 1.3;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 10px;
        }

        .header {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            text-align: center;
        }

        .header h1 {
            font-size: 1.8rem;
            color: #00d4ff;
            margin-bottom: 5px;
        }

        .stats {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .stat {
            background: rgba(255, 255, 255, 0.05);
            padding: 10px 15px;
            border-radius: 6px;
            text-align: center;
            min-width: 120px;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #888;
            margin-bottom: 3px;
        }

        .stat-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: #00d4ff;
        }

        .connected { color: #00ff88; }
        .disconnected { color: #ff4757; }

        .table-container {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            overflow: hidden;
        }

        .table-wrapper {
            max-height: 80vh;
            overflow-y: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.85rem;
        }

        .data-table th {
            background: rgba(0, 0, 0, 0.4);
            color: #ffffff;
            padding: 10px 8px;
            text-align: left;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
            font-size: 0.75rem;
            text-transform: uppercase;
        }

        .data-table td {
            padding: 8px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .instrument-row {
            transition: none; /* Remove transitions for speed */
        }

        .instrument-row:hover {
            background: rgba(0, 212, 255, 0.05);
        }

        /* Cell Styling - Minimal for speed */
        .ticker-cell {
            font-weight: 600;
            color: #00d4ff;
        }

        .ltp-cell {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .price-positive { color: #00ff88; }
        .price-negative { color: #ff4757; }
        .price-neutral { color: #ffffff; }

        .volume-cell {
            color: #74b9ff;
        }

        /* Ultra-fast update indicator */
        .updated-cell {
            background: rgba(0, 255, 136, 0.2) !important;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #888;
        }

        /* Performance optimizations */
        .data-table {
            contain: layout style;
        }

        .instrument-row {
            contain: layout style;
        }

        /* Minimal scrollbar */
        .table-wrapper::-webkit-scrollbar {
            width: 6px;
        }

        .table-wrapper::-webkit-scrollbar-thumb {
            background: #00d4ff;
            border-radius: 3px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container { padding: 5px; }
            .stats { flex-direction: column; }
            .data-table { font-size: 0.75rem; }
            .data-table th, .data-table td { padding: 6px 4px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>⚡ Ultra-Fast Market Dashboard</h1>
            <p>Optimized for maximum speed - minimal latency updates</p>
        </div>

        <!-- Stats -->
        <div class="stats">
            <div class="stat">
                <div class="stat-label">WebSocket</div>
                <div class="stat-value" id="ws-status">Connecting...</div>
            </div>
            <div class="stat">
                <div class="stat-label">Instruments</div>
                <div class="stat-value" id="instrument-count">0</div>
            </div>
            <div class="stat">
                <div class="stat-label">Updates/sec</div>
                <div class="stat-value" id="update-rate">0</div>
            </div>
            <div class="stat">
                <div class="stat-label">Latency</div>
                <div class="stat-value" id="latency">0ms</div>
            </div>
            <div class="stat">
                <div class="stat-label">Last Update</div>
                <div class="stat-value" id="last-update">Never</div>
            </div>
        </div>

        <!-- Table -->
        <div class="table-container">
            <div class="table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Ticker</th>
                            <th>Security ID</th>
                            <th>Exchange</th>
                            <th>LTP</th>
                            <th>Change</th>
                            <th>Change %</th>
                            <th>Volume</th>
                            <th>High</th>
                            <th>Low</th>
                            <th>Open</th>
                            <th>Close</th>
                            <th>Updated</th>
                        </tr>
                    </thead>
                    <tbody id="data-table-body">
                        <tr class="loading">
                            <td colspan="12">
                                <div>⚡ Connecting to ultra-fast feed...</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Ultra-Fast Dashboard - Optimized for Speed
        class UltraFastDashboard {
            constructor() {
                this.socket = io({
                    transports: ['websocket'], // Force WebSocket only
                    upgrade: false,
                    rememberUpgrade: false
                });
                
                this.instruments = new Map();
                this.updateCount = 0;
                this.lastSecond = Date.now();
                this.updatesThisSecond = 0;
                
                // DOM elements (cached)
                this.tableBody = document.getElementById('data-table-body');
                
                // Performance tracking
                this.lastUpdateTime = 0;
                
                this.init();
            }

            init() {
                this.setupWebSocket();
                this.startPerformanceMonitoring();
                console.log('⚡ Ultra-Fast Dashboard Initialized');
            }

            setupWebSocket() {
                this.socket.on('connect', () => {
                    this.updateStatus('ws-status', 'Connected', 'connected');
                    console.log('⚡ Connected - Ultra-Fast Mode');
                });

                this.socket.on('disconnect', () => {
                    this.updateStatus('ws-status', 'Disconnected', 'disconnected');
                });

                this.socket.on('marketData', (data) => {
                    this.handleMarketData(data);
                });
            }

            handleMarketData(data) {
                const now = Date.now();
                const latency = data.timestamp ? now - new Date(data.timestamp).getTime() : 0;
                
                const instrumentKey = `${data.exchangeName}_${data.securityId}`;
                
                // Calculate changes efficiently
                const priceChange = data.ltp && data.dayClose ? data.ltp - data.dayClose : null;
                const percentageChange = priceChange && data.dayClose ? (priceChange / data.dayClose) * 100 : null;
                
                // Store enhanced data
                const enhancedData = {
                    ...data,
                    priceChange,
                    percentageChange,
                    lastUpdate: now
                };
                
                this.instruments.set(instrumentKey, enhancedData);
                
                // Update counters
                this.updateCount++;
                this.updatesThisSecond++;
                this.lastUpdateTime = now;
                
                // Update status immediately
                this.updateStatus('instrument-count', this.instruments.size);
                this.updateStatus('last-update', new Date().toLocaleTimeString());
                this.updateStatus('latency', `${latency}ms`);
                
                // Update row immediately - no batching for maximum speed
                this.updateInstrumentRow(instrumentKey, enhancedData);
            }

            updateInstrumentRow(instrumentKey, data) {
                let row = document.getElementById(`row-${instrumentKey}`);
                
                if (!row) {
                    row = this.createInstrumentRow(instrumentKey, data);
                    this.tableBody.appendChild(row);
                    
                    // Remove loading message
                    const loadingRow = this.tableBody.querySelector('.loading');
                    if (loadingRow) loadingRow.remove();
                }
                
                const changeClass = this.getChangeClass(data.priceChange);
                const changeSymbol = data.priceChange > 0 ? '+' : '';
                
                // Direct DOM updates for maximum speed
                const cells = row.cells;
                this.updateCellFast(cells[0], data.ticker || 'N/A');
                this.updateCellFast(cells[1], data.securityId || 'N/A');
                this.updateCellFast(cells[2], data.exchangeName || 'N/A');
                this.updateCellFast(cells[3], data.ltp ? `₹${data.ltp.toFixed(2)}` : 'N/A', `ltp-cell ${changeClass}`);
                this.updateCellFast(cells[4], data.priceChange ? `${changeSymbol}₹${data.priceChange.toFixed(2)}` : 'N/A', changeClass);
                this.updateCellFast(cells[5], data.percentageChange ? `${changeSymbol}${data.percentageChange.toFixed(2)}%` : 'N/A', changeClass);
                this.updateCellFast(cells[6], data.volume ? this.formatNumber(data.volume) : 'N/A', 'volume-cell');
                this.updateCellFast(cells[7], data.dayHigh ? `₹${data.dayHigh.toFixed(2)}` : 'N/A');
                this.updateCellFast(cells[8], data.dayLow ? `₹${data.dayLow.toFixed(2)}` : 'N/A');
                this.updateCellFast(cells[9], data.dayOpen ? `₹${data.dayOpen.toFixed(2)}` : 'N/A');
                this.updateCellFast(cells[10], data.dayClose ? `₹${data.dayClose.toFixed(2)}` : 'N/A');
                this.updateCellFast(cells[11], new Date().toLocaleTimeString());
                
                // Minimal flash effect
                row.classList.add('updated-cell');
                setTimeout(() => row.classList.remove('updated-cell'), 100);
            }

            createInstrumentRow(instrumentKey, data) {
                const row = document.createElement('tr');
                row.id = `row-${instrumentKey}`;
                row.className = 'instrument-row';
                
                const priceChange = data.priceChange;
                const percentageChange = data.percentageChange;
                const changeClass = this.getChangeClass(priceChange);
                const changeSymbol = priceChange > 0 ? '+' : '';
                
                row.innerHTML = `
                    <td class="ticker-cell">${data.ticker || 'N/A'}</td>
                    <td>${data.securityId || 'N/A'}</td>
                    <td>${data.exchangeName || 'N/A'}</td>
                    <td class="ltp-cell ${changeClass}">₹${data.ltp ? data.ltp.toFixed(2) : 'N/A'}</td>
                    <td class="${changeClass}">${priceChange ? `${changeSymbol}₹${priceChange.toFixed(2)}` : 'N/A'}</td>
                    <td class="${changeClass}">${percentageChange ? `${changeSymbol}${percentageChange.toFixed(2)}%` : 'N/A'}</td>
                    <td class="volume-cell">${data.volume ? this.formatNumber(data.volume) : 'N/A'}</td>
                    <td>₹${data.dayHigh ? data.dayHigh.toFixed(2) : 'N/A'}</td>
                    <td>₹${data.dayLow ? data.dayLow.toFixed(2) : 'N/A'}</td>
                    <td>₹${data.dayOpen ? data.dayOpen.toFixed(2) : 'N/A'}</td>
                    <td>₹${data.dayClose ? data.dayClose.toFixed(2) : 'N/A'}</td>
                    <td>${new Date().toLocaleTimeString()}</td>
                `;
                
                return row;
            }

            updateCellFast(cell, newValue, className = '') {
                if (cell.textContent !== newValue) {
                    cell.textContent = newValue;
                    if (className) cell.className = className;
                }
            }

            getChangeClass(priceChange) {
                if (priceChange > 0) return 'price-positive';
                if (priceChange < 0) return 'price-negative';
                return 'price-neutral';
            }

            formatNumber(num) {
                if (num >= 10000000) return (num / 10000000).toFixed(1) + 'Cr';
                if (num >= 100000) return (num / 100000).toFixed(1) + 'L';
                if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
                return num.toLocaleString();
            }

            updateStatus(elementId, value, className = '') {
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = value;
                    if (className) element.className = `stat-value ${className}`;
                }
            }

            startPerformanceMonitoring() {
                setInterval(() => {
                    this.updateStatus('update-rate', this.updatesThisSecond);
                    this.updatesThisSecond = 0;
                }, 1000);
            }
        }

        // Initialize ultra-fast dashboard
        const dashboard = new UltraFastDashboard();
        console.log('⚡ Ultra-Fast Dashboard Ready - Maximum Speed Mode');
    </script>
</body>
</html>
