"use strict";
// ============================================================================
// MAIN SERVER ENTRY POINT - TypeScript Optimized
// ============================================================================
Object.defineProperty(exports, "__esModule", { value: true });
require("dotenv/config");
const MarketFeedServer_1 = require("./MarketFeedServer");
const LoggerService_1 = require("../services/LoggerService");
// Global error handlers
process.on("uncaughtException", (error) => {
    LoggerService_1.logger.error("Uncaught Exception", {
        error: error.message,
        stack: error.stack,
    });
    process.exit(1);
});
process.on("unhandledRejection", (reason, promise) => {
    LoggerService_1.logger.error("Unhandled Rejection", { reason, promise });
    process.exit(1);
});
// Graceful shutdown handler
let server = null;
const gracefulShutdown = async (signal) => {
    LoggerService_1.logger.info(`Received ${signal}, starting graceful shutdown...`);
    if (server) {
        try {
            await server.shutdown();
            LoggerService_1.logger.info("Graceful shutdown completed");
            process.exit(0);
        }
        catch (error) {
            LoggerService_1.logger.error("Error during graceful shutdown", {
                error: error.message,
            });
            process.exit(1);
        }
    }
    else {
        process.exit(0);
    }
};
// Register shutdown handlers
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));
// Main function
async function main() {
    try {
        LoggerService_1.logger.info("🚀 Starting Dhan Market Feed Server - REAL DATA ONLY MODE");
        // Validate environment variables for real data
        if (!process.env.ACCESS_TOKEN || !process.env.CLIENT_ID) {
            LoggerService_1.logger.error("❌ MISSING CREDENTIALS: ACCESS_TOKEN and CLIENT_ID are required for REAL market data");
            LoggerService_1.logger.error("💡 Please set your Dhan API credentials in .env file");
            process.exit(1);
        }
        LoggerService_1.logger.info("✅ Credentials validated - proceeding with REAL data connection");
        // Create and start server
        server = new MarketFeedServer_1.DhanMarketFeedServer();
        await server.start();
        LoggerService_1.logger.info("🎉 Server startup completed successfully - STREAMING REAL MARKET DATA");
    }
    catch (error) {
        LoggerService_1.logger.error("❌ Failed to start server", {
            error: error.message,
        });
        process.exit(1);
    }
}
// Start the application
main().catch((error) => {
    LoggerService_1.logger.error("Application startup failed", { error: error.message });
    process.exit(1);
});
//# sourceMappingURL=main.js.map