# Dhan Market Feed Server

An ultra-fast market data server for the Dhan trading platform, built with Next.js, React, and Socket.IO.

## Features

- Real-time market data streaming
- WebSocket-based communication
- Modern React dashboard with Tailwind CSS
- TypeScript for type safety
- Automatic reconnection handling
- Excel file support for instrument configuration
- REST API for status information

## Prerequisites

- Node.js 16.x or later
- npm 7.x or later
- Dhan trading account with API access

## Setup

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd dhan-websocket-server
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Create a `.env` file in the root directory with the following variables:

   ```
   PORT=3000
   ACCESS_TOKEN=your_access_token
   CLIENT_ID=your_client_id
   SUBSCRIPTION_TYPE=quote
   INSTRUMENTS=NSE_EQ:1333,NSE_EQ:3045
   ```

4. Build the project:
   ```bash
   npm run build
   ```

## Development

To start the development server:

```bash
npm run dev
```

This will start both the Next.js development server and the WebSocket server concurrently.

## Production

To start the production server:

```bash
npm run build
npm start
```

## API Endpoints

- `GET /api/data`: Get server status and statistics
- WebSocket events:
  - `marketData`: Real-time market data updates
  - `initialData`: Initial market data on connection

## Dashboard

The dashboard is accessible at `http://localhost:3000` and includes:

- Real-time market data table
- Connection status indicator
- Message and instrument statistics
- Responsive design for all devices

## License

MIT
