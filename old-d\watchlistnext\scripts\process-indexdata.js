const fs = require("fs");
const path = require("path");

// Function to process and clean indexdata
function processIndexData() {
  try {
    const filePath = path.join(__dirname, "..", "indexdata_Sheet1.json");
    
    console.log("📊 Processing IndexData...");
    
    // Read the JSON file
    const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    console.log(`📋 Original Records: ${data.length}`);
    
    // Remove currency indices
    const currencySymbols = ['USDINR', 'EURINR', 'GBPINR', 'JPYINR', 'EURUSD', 'GBPUSD', 'USDJPY'];
    
    const filteredData = data.filter(record => {
      const symbol = record.SEM_TRADING_SYMBOL;
      return !currencySymbols.includes(symbol);
    });
    
    console.log(`📋 After removing currency indices: ${filteredData.length}`);
    console.log(`🗑️ Removed ${data.length - filteredData.length} currency indices`);
    
    // Sort by exchange and then by security ID
    filteredData.sort((a, b) => {
      if (a.SEM_EXM_EXCH_ID !== b.SEM_EXM_EXCH_ID) {
        return a.SEM_EXM_EXCH_ID.localeCompare(b.SEM_EXM_EXCH_ID);
      }
      return a.SEM_SMST_SECURITY_ID - b.SEM_SMST_SECURITY_ID;
    });
    
    // Create processed data with additional fields for WebSocket
    const processedData = filteredData.map(record => ({
      ...record,
      // Add fields needed for WebSocket subscription
      EXCHANGE_SEGMENT: record.SEM_SEGMENT, // IDX_I
      SECURITY_ID: record.SEM_SMST_SECURITY_ID,
      SYMBOL: record.SEM_TRADING_SYMBOL,
      NAME: record.SEM_CUSTOM_SYMBOL,
      EXCHANGE: record.SEM_EXM_EXCH_ID,
      // Add default values for market data
      LAST_PRICE: 0,
      CHANGE: 0,
      CHANGE_PERCENT: 0,
      VOLUME: 0,
      HIGH: 0,
      LOW: 0,
      OPEN: 0,
      CLOSE: 0
    }));
    
    // Save cleaned data
    const cleanedFilePath = path.join(__dirname, "..", "indexdata_cleaned.json");
    fs.writeFileSync(cleanedFilePath, JSON.stringify(processedData, null, 2));
    console.log(`✅ Saved cleaned data to: ${cleanedFilePath}`);
    
    // Create key indices mapping for components
    const keyIndices = {
      BSE: [],
      NSE: []
    };
    
    // Categorize key indices
    processedData.forEach(record => {
      const exchange = record.SEM_EXM_EXCH_ID;
      const name = record.SEM_CUSTOM_SYMBOL.toLowerCase();
      const symbol = record.SEM_TRADING_SYMBOL;
      
      // Define key indices criteria
      const isKeyIndex = (
        // Major broad market indices
        name.includes('sensex') || name.includes('nifty 50') || name.includes('nifty 100') ||
        name.includes('nifty 200') || name.includes('nifty 500') || name.includes('bse 100') ||
        name.includes('bse 200') || name.includes('bse 500') ||
        // Major sectoral indices
        name.includes('bank') || name.includes('it') || name.includes('auto') || 
        name.includes('pharma') || name.includes('metal') || name.includes('fmcg') ||
        name.includes('realty') || name.includes('energy') || name.includes('healthcare') ||
        // Special indices
        symbol === 'FINNIFTY' || symbol === 'MIDCPNIFTY' || symbol === 'INDIA VIX'
      );
      
      if (isKeyIndex) {
        keyIndices[exchange].push({
          securityId: record.SEM_SMST_SECURITY_ID,
          symbol: record.SEM_TRADING_SYMBOL,
          name: record.SEM_CUSTOM_SYMBOL,
          exchange: record.SEM_EXM_EXCH_ID,
          segment: record.SEM_SEGMENT,
          lotUnits: record.SEM_LOT_UNITS
        });
      }
    });
    
    // Save key indices mapping
    const keyIndicesPath = path.join(__dirname, "..", "src", "data", "key-indices.json");
    
    // Ensure directory exists
    const keyIndicesDir = path.dirname(keyIndicesPath);
    if (!fs.existsSync(keyIndicesDir)) {
      fs.mkdirSync(keyIndicesDir, { recursive: true });
    }
    
    fs.writeFileSync(keyIndicesPath, JSON.stringify(keyIndices, null, 2));
    console.log(`✅ Saved key indices mapping to: ${keyIndicesPath}`);
    
    // Create WebSocket subscription list
    const subscriptionList = processedData.map(record => ({
      exchangeSegment: record.SEM_SEGMENT === 'IDX_I' ? 0 : 1, // IDX_I = 0 for indices
      securityId: record.SEM_SMST_SECURITY_ID.toString()
    }));
    
    const subscriptionPath = path.join(__dirname, "..", "src", "data", "index-subscriptions.json");
    fs.writeFileSync(subscriptionPath, JSON.stringify(subscriptionList, null, 2));
    console.log(`✅ Saved WebSocket subscription list to: ${subscriptionPath}`);
    
    // Print summary
    console.log("\n📊 PROCESSING SUMMARY:");
    console.log("=" .repeat(50));
    console.log(`📋 Total indices processed: ${processedData.length}`);
    console.log(`🔵 BSE indices: ${processedData.filter(r => r.SEM_EXM_EXCH_ID === 'BSE').length}`);
    console.log(`🟡 NSE indices: ${processedData.filter(r => r.SEM_EXM_EXCH_ID === 'NSE').length}`);
    console.log(`⭐ BSE key indices: ${keyIndices.BSE.length}`);
    console.log(`⭐ NSE key indices: ${keyIndices.NSE.length}`);
    console.log(`📡 WebSocket subscriptions: ${subscriptionList.length}`);
    
    console.log("\n⭐ KEY INDICES IDENTIFIED:");
    console.log("BSE Key Indices:");
    keyIndices.BSE.forEach(idx => {
      console.log(`  • ${idx.symbol} - ${idx.name} (ID: ${idx.securityId})`);
    });
    
    console.log("\nNSE Key Indices:");
    keyIndices.NSE.forEach(idx => {
      console.log(`  • ${idx.symbol} - ${idx.name} (ID: ${idx.securityId})`);
    });
    
    console.log("\n🎉 IndexData processing completed!");
    
    return {
      processedData,
      keyIndices,
      subscriptionList
    };
    
  } catch (error) {
    console.error("❌ Error processing IndexData:", error.message);
    throw error;
  }
}

// Run the processing
if (require.main === module) {
  processIndexData();
}

module.exports = { processIndexData };
