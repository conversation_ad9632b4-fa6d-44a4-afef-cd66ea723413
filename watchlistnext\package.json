{"name": "dhan-websocket-server", "version": "1.0.0", "description": "Ultra-fast market data server for Dhan trading platform", "scripts": {"dev": "concurrently \"npm run dev:next\" \"npm run dev:server\"", "dev:next": "next dev", "dev:server": "ts-node-dev --respawn --transpile-only --require tsconfig-paths/register src/server/index.ts", "build": "next build && tsc src/server/index.ts --outDir dist --module commonjs --target es2020 --esModuleInterop --allowSyntheticDefaultImports", "start": "concurrently \"next start\" \"node dist/server/index.js\"", "start:server": "node dist/server/index.js", "start:next": "next start", "lint": "next lint", "clean": "rm -rf dist .next"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "framer-motion": "^10.16.4", "multer": "^1.4.5-lts.1", "next": "^15.3.3", "react": "^18.2.0", "react-dom": "^18.2.0", "socket.io": "^4.6.1", "socket.io-client": "^4.8.1", "ws": "^8.13.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/multer": "^1.4.7", "@types/node": "^20.5.0", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@types/socket.io": "^3.0.2", "@types/socket.io-client": "^3.0.0", "@types/ws": "^8.5.5", "@types/xlsx": "^0.0.35", "autoprefixer": "^10.4.15", "concurrently": "^8.2.1", "postcss": "^8.4.28", "tailwindcss": "^3.3.3", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.6"}}