const xlsx = require('xlsx');
const { Client } = require('pg');
const path = require('path');

// Simple client connection
const client = new Client({
  host: 'aws-0-ap-south-1.pooler.supabase.com',
  port: 5432,
  database: 'postgres',
  user: 'postgres.fjognbnryybyeepcoukr',
  password: 'kuY9zaXlutHN8e76',
  ssl: {
    rejectUnauthorized: false
  }
});

async function main() {
  try {
    console.log('🚀 Starting company list import process...');
    
    // Read Excel file
    const filePath = path.join(__dirname, 'Companylist.xlsx');
    console.log('📖 Reading Excel file:', filePath);
    
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const companies = xlsx.utils.sheet_to_json(worksheet);
    
    console.log(`📊 Found ${companies.length} companies in Excel file`);
    
    // Connect to database
    console.log('🔗 Connecting to database...');
    await client.connect();
    console.log('✅ Connected to database');
    
    // Create table
    console.log('🏗️ Creating table...');
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS company_list (
        id SERIAL PRIMARY KEY,
        company_name VARCHAR(500) NOT NULL,
        isin_no VARCHAR(20),
        instrument VARCHAR(50),
        sector_name VARCHAR(200),
        industry_new_name VARCHAR(200),
        sub_sector VARCHAR(200),
        micro_category VARCHAR(200),
        bse_security_id VARCHAR(20),
        nse_security_id VARCHAR(20),
        symbol VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    await client.query(createTableQuery);
    console.log('✅ Table created successfully');
    
    // Clear existing data
    console.log('🗑️ Clearing existing data...');
    await client.query('DELETE FROM company_list');
    
    // Insert data in batches
    console.log('💾 Inserting company data...');
    const batchSize = 100;
    let successCount = 0;
    
    for (let i = 0; i < companies.length; i += batchSize) {
      const batch = companies.slice(i, i + batchSize);
      
      for (const company of batch) {
        try {
          const nseSecurityId = company.NSE_SECURITY_ID === '-' || !company.NSE_SECURITY_ID ? null : company.NSE_SECURITY_ID.toString();
          const bseSecurityId = company.BSE_SECURITY_ID ? company.BSE_SECURITY_ID.toString() : null;

          await client.query(`
            INSERT INTO company_list (
              company_name, isin_no, instrument, sector_name, industry_new_name,
              sub_sector, micro_category, bse_security_id, nse_security_id, symbol
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
          `, [
            company.COMPANY_NAME || '',
            company['ISIN No'] || '',
            company.Instrument || '',
            company['Sector Name'] || '',
            company['Industry New Name'] || '',
            company['Sub-Sector'] || '',
            company['Micro Category'] || '',
            bseSecurityId,
            nseSecurityId,
            company.SYMBOL || ''
          ]);
          successCount++;
        } catch (error) {
          console.error(`❌ Error inserting ${company.COMPANY_NAME}:`, error.message);
        }
      }
      
      console.log(`📈 Processed ${Math.min(i + batchSize, companies.length)} / ${companies.length} companies`);
    }
    
    // Show results
    const countResult = await client.query('SELECT COUNT(*) FROM company_list');
    console.log(`✅ Successfully inserted: ${successCount} companies`);
    console.log(`📊 Total companies in database: ${countResult.rows[0].count}`);
    
    // Show sample data
    const sampleResult = await client.query('SELECT * FROM company_list LIMIT 5');
    console.log('\n📋 Sample data from database:');
    console.table(sampleResult.rows);
    
    console.log('🎉 Import process completed successfully!');
    
  } catch (error) {
    console.error('❌ Import process failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await client.end();
  }
}

main();
