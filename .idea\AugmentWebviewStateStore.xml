<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;d7332f83-a783-455a-bae0-8750a046880b&quot;,&quot;conversations&quot;:{&quot;d66516f8-0223-40c0-b917-483f7e420cdb&quot;:{&quot;id&quot;:&quot;d66516f8-0223-40c0-b917-483f7e420cdb&quot;,&quot;createdAtIso&quot;:&quot;2025-06-12T06:10:06.625Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-12T06:10:06.625Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;effc59c8-ec61-4fc4-a1fc-d23c106b4fcf&quot;},&quot;d7332f83-a783-455a-bae0-8750a046880b&quot;:{&quot;id&quot;:&quot;d7332f83-a783-455a-bae0-8750a046880b&quot;,&quot;createdAtIso&quot;:&quot;2025-06-12T06:10:06.878Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-12T06:10:37.827Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;e983528a-5b50-40cc-bf4b-459adab65627&quot;,&quot;request_message&quot;:&quot;fix issues&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;fix issues&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended because we believe you may be using multiple free trials. Please contact support [here](https://docs.augmentcode.com/troubleshooting/support) for more information.*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;fix issues&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/websocketdhan/dhan-websocket-server&quot;,&quot;folderRoot&quot;:&quot;D:/websocketdhan/dhan-websocket-server&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-12T06:10:37.827Z&quot;,&quot;response_text&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended because we believe you may be using multiple free trials. Please contact support [here](https://docs.augmentcode.com/troubleshooting/support) for more information.*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-67491909-adca-4d84-830e-ebef07e69701&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;2742b4dd-23ee-43a2-ad01-e6b0743129f9&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>