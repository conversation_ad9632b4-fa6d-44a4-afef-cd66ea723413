"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import keyIndicesData from '../data/key-indices.json';

interface IndexData {
  securityId: number;
  symbol: string;
  name: string;
  exchange: string;
  segment: string;
  lotUnits: number;
  lastPrice?: number;
  change?: number;
  changePercent?: number;
  volume?: number;
  high?: number;
  low?: number;
}

interface MarketData {
  [securityId: string]: {
    ltp: number;
    change: number;
    changePercent: number;
    volume: number;
    high: number;
    low: number;
    open: number;
    close: number;
  };
}

const KeyIndicesComponent: React.FC = () => {
  const [marketData, setMarketData] = useState<MarketData>({});
  const [selectedExchange, setSelectedExchange] = useState<'BSE' | 'NSE' | 'ALL'>('ALL');
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // Simulate WebSocket connection for market data
    const fetchMarketData = async () => {
      try {
        const response = await fetch('/api/data');
        const data = await response.json();
        
        setIsConnected(data.connected);
        
        // Convert array to map for easier lookup
        const dataMap: MarketData = {};
        data.latestData?.forEach((item: any) => {
          dataMap[item.securityId] = {
            ltp: item.ltp || 0,
            change: item.change || 0,
            changePercent: item.changePercent || 0,
            volume: item.volume || 0,
            high: item.high || 0,
            low: item.low || 0,
            open: item.open || 0,
            close: item.close || 0,
          };
        });
        
        setMarketData(dataMap);
      } catch (error) {
        console.error('Error fetching market data:', error);
        setIsConnected(false);
      }
    };

    // Initial fetch
    fetchMarketData();

    // Set up polling for real-time updates
    const interval = setInterval(fetchMarketData, 1000);

    return () => clearInterval(interval);
  }, []);

  const getFilteredIndices = () => {
    if (selectedExchange === 'ALL') {
      return [...keyIndicesData.BSE, ...keyIndicesData.NSE];
    }
    return keyIndicesData[selectedExchange] || [];
  };

  const enrichIndexWithMarketData = (index: any): IndexData => {
    const marketInfo = marketData[index.securityId.toString()] || {};
    
    return {
      ...index,
      lastPrice: marketInfo.ltp || 0,
      change: marketInfo.change || 0,
      changePercent: marketInfo.changePercent || 0,
      volume: marketInfo.volume || 0,
      high: marketInfo.high || 0,
      low: marketInfo.low || 0,
    };
  };

  const formatNumber = (num: number, decimals: number = 2): string => {
    if (num === 0) return '0.00';
    return num.toFixed(decimals);
  };

  const formatVolume = (volume: number): string => {
    if (volume >= 10000000) return `${(volume / 10000000).toFixed(1)}Cr`;
    if (volume >= 100000) return `${(volume / 100000).toFixed(1)}L`;
    if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;
    return volume.toString();
  };

  const getChangeColor = (change: number): string => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getChangeBgColor = (change: number): string => {
    if (change > 0) return 'bg-green-50 border-green-200';
    if (change < 0) return 'bg-red-50 border-red-200';
    return 'bg-gray-50 border-gray-200';
  };

  const filteredIndices = getFilteredIndices();
  const enrichedIndices = filteredIndices.map(enrichIndexWithMarketData);

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-4">
          <h2 className="text-2xl font-bold text-gray-800">Key Indices</h2>
          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
            isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              isConnected ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            <span>{isConnected ? 'Live' : 'Disconnected'}</span>
          </div>
        </div>
        
        <div className="flex space-x-2">
          {['ALL', 'BSE', 'NSE'].map((exchange) => (
            <button
              key={exchange}
              onClick={() => setSelectedExchange(exchange as 'BSE' | 'NSE' | 'ALL')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedExchange === exchange
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {exchange}
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {enrichedIndices.map((index, i) => (
          <motion.div
            key={`${index.exchange}-${index.securityId}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: i * 0.05 }}
            className={`p-4 rounded-lg border-2 transition-all hover:shadow-md ${getChangeBgColor(index.change || 0)}`}
          >
            <div className="flex justify-between items-start mb-2">
              <div>
                <h3 className="font-bold text-lg text-gray-800">{index.symbol}</h3>
                <p className="text-sm text-gray-600 truncate" title={index.name}>
                  {index.name}
                </p>
              </div>
              <span className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded">
                {index.exchange}
              </span>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-gray-800">
                  {formatNumber(index.lastPrice || 0)}
                </span>
                <div className={`text-right ${getChangeColor(index.change || 0)}`}>
                  <div className="font-semibold">
                    {index.change && index.change !== 0 ? (index.change > 0 ? '+' : '') + formatNumber(index.change) : '0.00'}
                  </div>
                  <div className="text-sm">
                    ({index.changePercent && index.changePercent !== 0 ? (index.changePercent > 0 ? '+' : '') + formatNumber(index.changePercent) : '0.00'}%)
                  </div>
                </div>
              </div>

              <div className="flex justify-between text-sm text-gray-600">
                <div>
                  <span className="block">High: {formatNumber(index.high || 0)}</span>
                  <span className="block">Low: {formatNumber(index.low || 0)}</span>
                </div>
                <div className="text-right">
                  <span className="block">Vol: {formatVolume(index.volume || 0)}</span>
                  <span className="block">ID: {index.securityId}</span>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {enrichedIndices.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg">No indices found for {selectedExchange}</div>
          <div className="text-gray-400 text-sm mt-2">
            Try selecting a different exchange or check your connection
          </div>
        </div>
      )}

      <div className="mt-6 text-center text-sm text-gray-500">
        Showing {enrichedIndices.length} indices • Updated every second
      </div>
    </div>
  );
};

export default KeyIndicesComponent;
