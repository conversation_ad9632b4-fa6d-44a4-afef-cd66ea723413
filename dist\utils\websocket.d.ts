import { Socket } from "socket.io-client";
interface WebSocketOptions {
    onConnect?: () => void;
    onDisconnect?: (reason: string) => void;
    onError?: (error: Error) => void;
    onReconnect?: (attemptNumber: number) => void;
    onMarketData?: (data: any) => void;
    onMarketDataBatch?: (data: any[]) => void;
}
export declare const createWebSocketConnection: (options?: WebSocketOptions) => Socket;
export {};
//# sourceMappingURL=websocket.d.ts.map