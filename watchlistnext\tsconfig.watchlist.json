{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": false, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"], "ts-node": {"require": ["tsconfig-paths/register"]}}