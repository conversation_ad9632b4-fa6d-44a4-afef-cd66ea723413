"use strict";
// ============================================================================
// DHAN MARKET FEED SERVER - TypeScript Optimized Version
// ============================================================================
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DhanMarketFeedServer = void 0;
require("dotenv/config");
const ws_1 = __importDefault(require("ws"));
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const CacheService_1 = require("../services/CacheService");
const LoggerService_1 = require("../services/LoggerService");
const DataService_1 = require("../services/DataService");
// Exchange Segment Constants
const EXCHANGE_SEGMENTS = {
    IDX_I: 0,
    NSE_EQ: 1,
    NSE_FNO: 2,
    NSE_CURRENCY: 3,
    BSE_EQ: 4,
    MCX_COMM: 5,
    BSE_CURRENCY: 7,
    BSE_FNO: 8,
};
// Subscription Type Constants
const SUBSCRIPTION_TYPES = {
    ticker: 15,
    quote: 17,
    full: 21,
};
class DhanMarketFeedServer {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.messageCount = 0;
        this.instruments = [];
        // Optimized data storage
        this.liveData = new Map();
        this.previousCloseData = new Map();
        // Performance monitoring
        this.lastHealthCheck = 0;
        this.connectionAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.accessToken = process.env.ACCESS_TOKEN?.trim() || "";
        this.clientId = process.env.CLIENT_ID?.trim() || "";
        this.subscriptionType = process.env.SUBSCRIPTION_TYPE?.trim() || "full";
        this.port = parseInt(process.env.PORT || "8080");
        this.app = (0, express_1.default)();
        this.server = (0, http_1.createServer)(this.app);
        this.io = new socket_io_1.Server(this.server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"],
            },
        });
        this.validateConfig();
        this.setupWebServer();
        this.setupSocketHandlers();
    }
    /**
     * Validate configuration
     */
    validateConfig() {
        if (!this.accessToken || !this.clientId) {
            LoggerService_1.logger.error("ACCESS_TOKEN and CLIENT_ID are required for live data");
            throw new Error("ACCESS_TOKEN and CLIENT_ID must be set in .env file for real market data");
        }
        if (!SUBSCRIPTION_TYPES[this.subscriptionType]) {
            throw new Error(`Invalid SUBSCRIPTION_TYPE. Must be one of: ${Object.keys(SUBSCRIPTION_TYPES).join(", ")}`);
        }
        LoggerService_1.logger.info("Configuration validated successfully", {
            subscriptionType: this.subscriptionType,
            port: this.port,
            hasCredentials: true,
        });
    }
    /**
     * Load instruments from database
     */
    async loadInstruments() {
        try {
            const cacheKey = CacheService_1.CacheKeys.instruments();
            let instruments = CacheService_1.cacheService.get(cacheKey);
            if (!instruments) {
                // Load from database with environment-based filtering
                const maxInstruments = parseInt(process.env.MAX_INSTRUMENTS || "1000");
                const preferredExchange = process.env.PREFERRED_EXCHANGE || "NSE";
                const sectors = process.env.SECTORS?.split(",").map((s) => s.trim()) || [];
                LoggerService_1.logger.info("Loading instruments from database", {
                    maxInstruments,
                    preferredExchange,
                    sectors: sectors.length,
                });
                // Load both NSE and BSE instruments
                const nseCompanies = await DataService_1.dataService.getCompanies(maxInstruments / 2, 0, undefined, "NSE");
                const bseCompanies = await DataService_1.dataService.getCompanies(maxInstruments / 2, 0, undefined, "BSE");
                const allCompanies = [
                    ...nseCompanies.companies,
                    ...bseCompanies.companies,
                ];
                instruments = [];
                // Process each company to extract both NSE and BSE instruments
                allCompanies.forEach((company) => {
                    // Add NSE instrument if available
                    if (company.nse_security_id &&
                        company.nse_symbol &&
                        company.nse_symbol !== "-") {
                        instruments.push({
                            securityId: parseInt(company.nse_security_id),
                            ticker: company.nse_symbol,
                            exchange: "NSE_EQ",
                            exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
                            segment: "NSE_EQ",
                            lotUnits: 1,
                            name: company.company_name,
                        });
                    }
                    // Add BSE instrument if available (using company name as ticker since bse_symbol doesn't exist)
                    if (company.bse_security_id && company.bse_security_id !== "-") {
                        instruments.push({
                            securityId: parseInt(company.bse_security_id),
                            ticker: company.company_name
                                .replace(/[^A-Z0-9]/gi, "")
                                .toUpperCase()
                                .slice(0, 20), // Create ticker from company name
                            exchange: "BSE_EQ",
                            exchangeCode: EXCHANGE_SEGMENTS.BSE_EQ,
                            segment: "BSE_EQ",
                            lotUnits: 1,
                            name: company.company_name,
                        });
                    }
                });
                // Remove duplicates and limit to maxInstruments
                instruments = instruments
                    .filter((inst, index, self) => index ===
                    self.findIndex((i) => i.securityId === inst.securityId &&
                        i.exchange === inst.exchange))
                    .slice(0, maxInstruments);
                // Cache for 1 hour
                CacheService_1.cacheService.set(cacheKey, instruments, 3600000);
                LoggerService_1.logger.info("Instruments loaded from database", {
                    count: instruments.length,
                    nseCount: instruments.filter((i) => i.exchange === "NSE_EQ").length,
                    bseCount: instruments.filter((i) => i.exchange === "BSE_EQ").length,
                });
            }
            else {
                LoggerService_1.logger.info("Instruments loaded from cache", {
                    count: instruments.length,
                });
            }
            this.instruments = instruments;
            if (this.instruments.length === 0) {
                // Fallback instruments
                this.instruments = [
                    {
                        securityId: 1333,
                        ticker: "HDFCBANK",
                        exchange: "NSE_EQ",
                        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
                        segment: "NSE_EQ",
                        lotUnits: 1,
                    },
                    {
                        securityId: 3045,
                        ticker: "RELIANCE",
                        exchange: "NSE_EQ",
                        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
                        segment: "NSE_EQ",
                        lotUnits: 1,
                    },
                ];
                LoggerService_1.logger.warn("Using fallback instruments");
            }
        }
        catch (error) {
            LoggerService_1.logger.error("Error loading instruments", {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Setup web server routes
     */
    setupWebServer() {
        // CORS middleware
        this.app.use((req, res, next) => {
            res.header("Access-Control-Allow-Origin", "*");
            res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization");
            if (req.method === "OPTIONS") {
                res.sendStatus(200);
            }
            else {
                next();
            }
        });
        // JSON middleware
        this.app.use(express_1.default.json());
        // Health check endpoint
        this.app.get("/health", async (req, res) => {
            const cacheKey = CacheService_1.CacheKeys.health();
            let healthData = CacheService_1.cacheService.get(cacheKey);
            if (!healthData) {
                const dbConnected = await DataService_1.dataService.checkConnection();
                const watchlists = await DataService_1.dataService.getWatchlists();
                healthData = {
                    status: "healthy",
                    database: {
                        connected: dbConnected,
                        watchlists: watchlists.length,
                    },
                    websocket: {
                        connected: this.isConnected,
                        connections: this.io.engine.clientsCount,
                        instruments: this.instruments.length,
                    },
                    timestamp: Date.now(),
                };
                // Cache for 30 seconds
                CacheService_1.cacheService.set(cacheKey, healthData, 30000);
            }
            res.json(healthData);
        });
        // Indices endpoint with caching - REAL DATA ONLY
        this.app.get("/api/indices", async (req, res) => {
            try {
                const limit = parseInt(req.query.limit) || 100;
                const cacheKey = CacheService_1.CacheKeys.indices(limit);
                let response = CacheService_1.cacheService.get(cacheKey);
                if (!response) {
                    // Get index instruments from both NSE and BSE
                    const indexInstruments = this.instruments
                        .filter((inst) => {
                        const ticker = inst.ticker.toUpperCase();
                        const name = inst.name?.toLowerCase() || "";
                        // NSE Index patterns
                        const isNSEIndex = inst.exchange === "NSE_EQ" &&
                            (ticker.includes("NIFTY") ||
                                ticker.includes("BANK") ||
                                ticker.includes("AUTO") ||
                                ticker.includes("IT") ||
                                ticker.includes("FMCG") ||
                                ticker.includes("METAL") ||
                                ticker.includes("REALTY") ||
                                ticker.includes("PHARMA") ||
                                ticker.includes("ENERGY") ||
                                ticker.includes("MIDCAP") ||
                                ticker.includes("FIN") ||
                                ticker.includes("INDEX") ||
                                name.includes("index"));
                        // BSE Index patterns
                        const isBSEIndex = inst.exchange === "BSE_EQ" &&
                            (ticker.includes("SENSEX") ||
                                ticker.includes("BSE") ||
                                ticker.includes("BANKEX") ||
                                ticker.includes("AUTO") ||
                                ticker.includes("IT") ||
                                ticker.includes("FMCG") ||
                                ticker.includes("METAL") ||
                                ticker.includes("REALTY") ||
                                ticker.includes("HC") ||
                                ticker.includes("POWER") ||
                                ticker.includes("MIDCAP") ||
                                ticker.includes("FIN") ||
                                ticker.includes("INDEX") ||
                                name.includes("index"));
                        return isNSEIndex || isBSEIndex;
                    })
                        .slice(0, limit);
                    // Get real market data for indices only
                    const indexData = indexInstruments
                        .map((instrument) => {
                        const key = `${instrument.exchangeCode}-${instrument.securityId}`;
                        const marketInfo = this.liveData.get(key);
                        // Only return real data if available
                        if (marketInfo && marketInfo.ltp && marketInfo.ltp > 0) {
                            return marketInfo;
                        }
                        return null;
                    })
                        .filter((item) => item !== null);
                    response = {
                        connected: this.isConnected,
                        indices: indexData,
                        totalIndices: indexData.length,
                        activeIndices: indexData.filter((item) => item.ltp > 0)
                            .length,
                    };
                    // Cache for 2 seconds (real-time feel)
                    CacheService_1.cacheService.set(cacheKey, response, 2000);
                }
                res.json(response);
            }
            catch (error) {
                LoggerService_1.logger.error("Error in /api/indices endpoint", {
                    error: error.message,
                });
                res.status(500).json({
                    success: false,
                    error: "Internal server error",
                    timestamp: Date.now(),
                });
            }
        });
        // Statistics endpoint
        this.app.get("/api/stats", (req, res) => {
            const stats = {
                connected: this.isConnected,
                totalMessages: this.messageCount,
                instrumentCount: this.instruments.length,
                subscriptionType: this.subscriptionType,
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage(),
                cacheStats: CacheService_1.cacheService.getStats(),
                dbStats: DataService_1.dataService.getPoolStats(),
                timestamp: Date.now(),
            };
            res.json(stats);
        });
        // Live data endpoint
        this.app.get("/api/live", (req, res) => {
            const liveDataArray = Array.from(this.liveData.values());
            res.json({
                timestamp: Date.now(),
                connected: this.isConnected,
                totalInstruments: this.liveData.size,
                totalMessages: this.messageCount,
                data: liveDataArray.slice(0, 100), // Limit to 100 for performance
            });
        });
        // Stock endpoint
        this.app.get("/api/stock/:ticker", async (req, res) => {
            try {
                const ticker = req.params.ticker?.toUpperCase();
                if (!ticker) {
                    return res.status(400).json({
                        error: "Ticker parameter is required",
                        message: "Please provide a valid ticker symbol",
                    });
                }
                // First, try to find the company in our database
                try {
                    const { companies } = await DataService_1.dataService.getCompanies(1, 0, undefined, undefined, ticker);
                    const company = companies[0];
                    if (!company) {
                        return res.status(404).json({
                            error: "Stock not found",
                            message: `Could not find company with ticker "${ticker}" in our database. Please verify the ticker symbol and try again.`,
                            suggestions: [
                                "Check if the ticker symbol is correct",
                                "Try using the exact NSE/BSE ticker symbol",
                            ],
                        });
                    }
                    // Try to find the stock by NSE security ID first
                    let stock = null;
                    if (company.nse_security_id && company.nse_security_id !== "-") {
                        const nseKey = `${EXCHANGE_SEGMENTS.NSE_EQ}-${company.nse_security_id}`;
                        stock = this.liveData.get(nseKey);
                    }
                    // If not found in NSE, try BSE
                    if (!stock &&
                        company.bse_security_id &&
                        company.bse_security_id !== "-") {
                        const bseKey = `${EXCHANGE_SEGMENTS.BSE_EQ}-${company.bse_security_id}`;
                        stock = this.liveData.get(bseKey);
                    }
                    if (!stock) {
                        return res.status(404).json({
                            error: "Stock not found",
                            message: `Could not find live market data for "${ticker}". The stock may not be actively trading.`,
                            suggestions: [
                                "Make sure the stock is actively trading",
                                "Try again during market hours",
                            ],
                            company: {
                                name: company.company_name,
                                nse_symbol: company.nse_symbol,
                                bse_symbol: company.bse_symbol,
                                sector: company.sector_name,
                                industry: company.industry_name,
                            },
                        });
                    }
                    // Enhance the stock data with additional calculated fields
                    const enhancedStock = {
                        ...stock,
                        dayRange: `₹${stock.low?.toFixed(2) || "0.00"} - ₹${stock.high?.toFixed(2) || "0.00"}`,
                        weekRange52: `₹${(stock.low * 0.8)?.toFixed(2) || "0.00"} - ₹${(stock.high * 1.2)?.toFixed(2) || "0.00"}`, // Mock 52-week range
                        marketCap: this.calculateMarketCap(stock.ltp, stock.volume),
                        sector: company.sector_name || this.getSector(stock.ticker),
                        industry: company.industry_name || this.getIndustry(stock.ticker),
                        companyName: company.company_name,
                    };
                    return res.json(enhancedStock);
                }
                catch (error) {
                    LoggerService_1.logger.error("Error in /api/stock/:ticker endpoint", {
                        error: error.message,
                        ticker,
                    });
                    throw error;
                }
            }
            catch (error) {
                LoggerService_1.logger.error("Error in /api/stock/:ticker endpoint", {
                    error: error.message,
                    ticker: req.params.ticker,
                });
                return res.status(500).json({
                    error: "Internal server error",
                    message: "An unexpected error occurred while fetching stock data",
                });
            }
        });
        // Instruments breakdown endpoint
        this.app.get("/api/instruments", (req, res) => {
            const exchange = req.query.exchange;
            const limit = parseInt(req.query.limit) || 50;
            let filteredInstruments = this.instruments;
            if (exchange) {
                filteredInstruments = this.instruments.filter((inst) => inst.exchange.toLowerCase().includes(exchange.toLowerCase()));
            }
            const result = filteredInstruments.slice(0, limit);
            res.json({
                total: this.instruments.length,
                nseCount: this.instruments.filter((i) => i.exchange === "NSE_EQ")
                    .length,
                bseCount: this.instruments.filter((i) => i.exchange === "BSE_EQ")
                    .length,
                filtered: filteredInstruments.length,
                returned: result.length,
                instruments: result.map((inst) => ({
                    securityId: inst.securityId,
                    ticker: inst.ticker,
                    exchange: inst.exchange,
                    name: inst.name,
                })),
            });
        });
        // Market data endpoint with live prices (for frontend)
        this.app.get("/api/data", (req, res) => {
            const exchange = req.query.exchange;
            const limit = parseInt(req.query.limit) || 100;
            let filteredInstruments = this.instruments;
            if (exchange) {
                filteredInstruments = this.instruments.filter((inst) => inst.exchange.toLowerCase().includes(exchange.toLowerCase()));
            }
            // Get market data for instruments - only return instruments with real data
            const latestData = filteredInstruments
                .map((inst) => {
                const key = `${inst.exchangeCode}-${inst.securityId}`;
                const marketData = this.liveData.get(key);
                if (marketData && marketData.ltp && marketData.ltp > 0) {
                    // Return real market data only
                    return {
                        ticker: inst.ticker,
                        securityId: inst.securityId,
                        exchange: inst.exchange,
                        exchangeCode: inst.exchangeCode,
                        ltp: marketData.ltp,
                        change: marketData.change || 0,
                        changePercent: marketData.changePercent || 0,
                        volume: marketData.volume || 0,
                        high: marketData.high || 0,
                        low: marketData.low || 0,
                        open: marketData.open || 0,
                        close: marketData.close || 0,
                        timestamp: marketData.timestamp || Date.now(),
                    };
                }
                return null;
            })
                .filter((item) => item !== null)
                .slice(0, limit);
            res.json({
                connected: this.isConnected,
                messageCount: this.messageCount,
                instruments: filteredInstruments.length,
                subscriptionType: this.subscriptionType,
                latestData: latestData,
                timestamp: Date.now(),
            });
        });
        LoggerService_1.logger.info("Web server routes configured");
    }
    /**
     * Setup Socket.IO handlers
     */
    setupSocketHandlers() {
        this.io.on("connection", (socket) => {
            LoggerService_1.logger.info("Client connected", { socketId: socket.id });
            socket.on("subscribe", (data) => {
                LoggerService_1.logger.debug("Client subscription request", {
                    socketId: socket.id,
                    data,
                });
                // Handle subscription logic here
            });
            socket.on("disconnect", () => {
                LoggerService_1.logger.info("Client disconnected", { socketId: socket.id });
            });
        });
    }
    /**
     * Connect to Dhan WebSocket feed
     */
    async connectToMarketFeed() {
        if (this.isConnected) {
            LoggerService_1.logger.warn("Already connected to market feed");
            return;
        }
        try {
            this.connectionAttempts++;
            LoggerService_1.logger.info("Connecting to Dhan market feed", {
                attempt: this.connectionAttempts,
                url: "wss://api-feed.dhan.co",
            });
            // Require access token for real data - NO MOCK DATA
            if (!this.accessToken || !this.clientId) {
                LoggerService_1.logger.error("❌ ACCESS_TOKEN and CLIENT_ID are REQUIRED for real market data");
                throw new Error("Cannot connect to Dhan without valid credentials - NO MOCK DATA ALLOWED");
            }
            // Construct WebSocket URL with authentication parameters (Dhan API v2)
            const wsUrl = `wss://api-feed.dhan.co?version=2&token=${encodeURIComponent(this.accessToken)}&clientId=${encodeURIComponent(this.clientId)}&authType=2`;
            this.ws = new ws_1.default(wsUrl, {
                headers: {
                    "User-Agent": "Dhan-Market-Feed-Client/1.0",
                },
                handshakeTimeout: 10000,
            });
            this.ws.on("open", () => {
                this.isConnected = true;
                this.connectionAttempts = 0;
                LoggerService_1.logger.info("✅ CONNECTED TO DHAN LIVE MARKET FEED - REAL DATA STREAMING");
                // Wait for connection to stabilize, then subscribe (like working code)
                setTimeout(() => {
                    LoggerService_1.logger.info("🔄 Starting instrument subscription with CORRECT format...");
                    this.subscribeToInstruments();
                }, 1000);
            });
            this.ws.on("message", (data) => {
                this.handleMarketData(data);
            });
            this.ws.on("close", (code, reason) => {
                this.isConnected = false;
                LoggerService_1.logger.warn("WebSocket connection closed", {
                    code,
                    reason: reason.toString(),
                });
                // Attempt reconnection
                if (this.connectionAttempts < this.maxReconnectAttempts) {
                    setTimeout(() => {
                        this.connectToMarketFeed();
                    }, 5000);
                }
            });
            this.ws.on("error", (error) => {
                this.isConnected = false;
                LoggerService_1.logger.error("WebSocket error", { error: error.message });
            });
        }
        catch (error) {
            LoggerService_1.logger.error("Failed to connect to market feed", {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Subscribe to instruments
     */
    subscribeToInstruments() {
        if (!this.ws || this.ws.readyState !== ws_1.default.OPEN) {
            LoggerService_1.logger.error("Cannot subscribe - WebSocket not open");
            return;
        }
        // Subscribe in batches to avoid overwhelming the server
        const batchSize = 100;
        const batches = [];
        for (let i = 0; i < this.instruments.length; i += batchSize) {
            batches.push(this.instruments.slice(i, i + batchSize));
        }
        // Subscribe to batches with delay (like working code)
        for (let i = 0; i < batches.length; i++) {
            setTimeout(() => {
                this.subscribeToBatch(batches[i]);
            }, i * 200); // 200ms delay between batches (like working code)
        }
    }
    /**
     * Subscribe to a batch of instruments (CORRECTED Dhan API Format)
     */
    subscribeToBatch(instruments) {
        if (!this.ws || this.ws.readyState !== ws_1.default.OPEN) {
            LoggerService_1.logger.warn("Cannot subscribe - WebSocket not open");
            return;
        }
        const requestCode = SUBSCRIPTION_TYPES[this.subscriptionType];
        // CORRECT Dhan WebSocket API format (FIXED: Use STRING exchange like working code)
        const subscriptionMessage = {
            RequestCode: requestCode,
            InstrumentCount: instruments.length,
            InstrumentList: instruments.map((inst) => ({
                ExchangeSegment: inst.exchange, // ✅ Use STRING exchange like "NSE_EQ" (WORKING FORMAT)
                SecurityId: inst.securityId.toString(), // ✅ Ensure string format
            })),
        };
        try {
            this.ws.send(JSON.stringify(subscriptionMessage));
            LoggerService_1.logger.info("✅ Subscribed to instrument batch", {
                count: instruments.length,
                requestCode,
                sampleInstruments: instruments.slice(0, 3).map((i) => ({
                    ticker: i.ticker,
                    exchangeCode: i.exchangeCode,
                    securityId: i.securityId,
                })),
            });
        }
        catch (error) {
            LoggerService_1.logger.error("❌ Failed to send subscription", {
                error: error.message,
                count: instruments.length,
            });
        }
    }
    /**
     * Handle incoming REAL market data from Dhan WebSocket
     */
    handleMarketData(data) {
        try {
            this.messageCount++;
            // Only log every 1000th message to reduce noise
            if (this.messageCount % 1000 === 0) {
                LoggerService_1.logger.debug("📊 Market data stats", {
                    messageCount: this.messageCount,
                    dataType: typeof data,
                });
            }
            // Convert RawData to Buffer
            let buffer;
            if (Buffer.isBuffer(data)) {
                buffer = data;
            }
            else if (data instanceof ArrayBuffer) {
                buffer = Buffer.from(data);
            }
            else if (Array.isArray(data)) {
                buffer = Buffer.concat(data);
            }
            else {
                LoggerService_1.logger.warn("Unknown data type received from Dhan WebSocket", {
                    dataType: typeof data,
                });
                return;
            }
            // Parse binary data from Dhan
            const message = this.parseMarketData(buffer);
            if (message && message.ltp && message.ltp > 0) {
                // Store REAL live data
                const key = `${message.exchangeCode}-${message.securityId}`;
                this.liveData.set(key, message);
                // Only log significant price changes (>1%) to reduce noise
                const previousData = this.liveData.get(key);
                if (previousData &&
                    Math.abs((message.ltp - previousData.ltp) / previousData.ltp) > 0.01) {
                    LoggerService_1.logger.info("Significant price change", {
                        ticker: message.ticker,
                        oldPrice: previousData.ltp,
                        newPrice: message.ltp,
                        change: (((message.ltp - previousData.ltp) / previousData.ltp) *
                            100).toFixed(2) + "%",
                    });
                }
                // Broadcast REAL data to connected clients
                this.io.emit("marketData", message);
                // Update cache with REAL data
                const cacheKey = CacheService_1.CacheKeys.marketData(message.securityId);
                CacheService_1.cacheService.set(cacheKey, message, 5000); // 5 second cache
            }
        }
        catch (error) {
            LoggerService_1.logger.error("Error handling REAL market data", {
                error: error.message,
            });
        }
    }
    /**
     * Parse binary market data (Real implementation from working codebase)
     */
    parseMarketData(data) {
        try {
            if (!(data instanceof Buffer)) {
                return null;
            }
            // Parse binary message header (8 bytes)
            const responseCode = data.readUInt8(0);
            const messageLength = data.readUInt16LE(1);
            const exchangeSegment = data.readUInt8(3);
            const securityId = data.readUInt32LE(4);
            const exchangeName = this.getExchangeSegmentName(exchangeSegment);
            const timestamp = Date.now();
            const instrumentKey = `${exchangeSegment}-${securityId}`;
            // Find ticker for this instrument
            const instrument = this.instruments.find((inst) => inst.securityId === securityId &&
                EXCHANGE_SEGMENTS[inst.exchange] === exchangeSegment);
            const ticker = instrument ? instrument.ticker : `UNKNOWN_${securityId}`;
            let marketData = {
                ticker,
                securityId: securityId.toString(),
                exchange: exchangeName,
                exchangeCode: exchangeSegment,
                timestamp,
                responseCode,
                dataLength: data.length,
            };
            // Parse different response types based on response code
            switch (responseCode) {
                case 2: // Ticker packet
                    if (data.length >= 16) {
                        marketData.ltp = data.readFloatLE(8);
                        marketData.ltt = data.readUInt32LE(12);
                    }
                    break;
                case 4: // Quote packet
                    if (data.length >= 50) {
                        marketData.ltp = data.readFloatLE(8);
                        marketData.ltq = data.readUInt16LE(12);
                        marketData.ltt = data.readUInt32LE(14);
                        marketData.atp = data.readFloatLE(18);
                        marketData.volume = data.readUInt32LE(22);
                        marketData.totalSellQuantity = data.readUInt32LE(26);
                        marketData.totalBuyQuantity = data.readUInt32LE(30);
                        marketData.open = data.readFloatLE(34);
                        marketData.close = data.readFloatLE(38);
                        marketData.high = data.readFloatLE(42);
                        marketData.low = data.readFloatLE(46);
                        // Calculate change if we have close price
                        if (marketData.ltp &&
                            marketData.close &&
                            marketData.close !== marketData.ltp) {
                            marketData.change = marketData.ltp - marketData.close;
                            marketData.changePercent =
                                (marketData.change / marketData.close) * 100;
                        }
                    }
                    break;
                case 5: // Open Interest packet
                    if (data.length >= 12) {
                        marketData.openInterest = data.readUInt32LE(8);
                    }
                    break;
                case 6: // Previous close packet
                    if (data.length >= 16) {
                        marketData.previousClose = data.readFloatLE(8);
                        marketData.previousOI = data.readUInt32LE(12);
                    }
                    break;
                case 8: // Full packet (with market depth)
                    if (data.length >= 162) {
                        marketData.ltp = data.readFloatLE(8);
                        marketData.ltq = data.readUInt16LE(12);
                        marketData.ltt = data.readUInt32LE(14);
                        marketData.atp = data.readFloatLE(18);
                        marketData.volume = data.readUInt32LE(22);
                        marketData.totalSellQuantity = data.readUInt32LE(26);
                        marketData.totalBuyQuantity = data.readUInt32LE(30);
                        marketData.openInterest = data.readUInt32LE(34);
                        marketData.open = data.readFloatLE(46);
                        marketData.close = data.readFloatLE(50);
                        marketData.high = data.readFloatLE(54);
                        marketData.low = data.readFloatLE(58);
                        // Calculate change
                        if (marketData.ltp &&
                            marketData.close &&
                            marketData.close !== marketData.ltp) {
                            marketData.change = marketData.ltp - marketData.close;
                            marketData.changePercent =
                                (marketData.change / marketData.close) * 100;
                        }
                    }
                    break;
                default:
                    // Unknown response code
                    return null;
            }
            return marketData;
        }
        catch (error) {
            LoggerService_1.logger.error("Error parsing market data", {
                error: error.message,
            });
            return null;
        }
    }
    /**
     * Get exchange segment name from code
     */
    getExchangeSegmentName(code) {
        const reverseMap = {};
        Object.entries(EXCHANGE_SEGMENTS).forEach(([key, value]) => {
            reverseMap[value] = key;
        });
        return reverseMap[code] || `Unknown(${code})`;
    }
    calculateMarketCap(ltp, volume) {
        // This is a simplified calculation - in reality, you'd need shares outstanding
        const mockShares = volume * 1000; // Mock shares outstanding
        const marketCap = ltp * mockShares;
        if (marketCap >= *********00) {
            // 1000 Cr
            return `₹${(marketCap / ********).toFixed(2)} Cr`;
        }
        else if (marketCap >= *********) {
            // 10 Cr
            return `₹${(marketCap / ********).toFixed(2)} Cr`;
        }
        else {
            return `₹${(marketCap / 100000).toFixed(2)} L`;
        }
    }
    getSector(ticker) {
        const sectorMap = {
            RELIANCE: "Energy",
            TCS: "Information Technology",
            INFY: "Information Technology",
            HDFCBANK: "Financial Services",
            ICICIBANK: "Financial Services",
            BHARTIARTL: "Telecommunication",
            ITC: "FMCG",
            SBIN: "Financial Services",
            LT: "Construction",
            HCLTECH: "Information Technology",
            MARUTI: "Automobile",
            BAJFINANCE: "Financial Services",
            ASIANPAINT: "Paints",
            NESTLEIND: "FMCG",
            KOTAKBANK: "Financial Services",
            WOCKPHARMA: "Healthcare",
        };
        return sectorMap[ticker.toUpperCase()] || "Others";
    }
    getIndustry(ticker) {
        const industryMap = {
            RELIANCE: "Oil & Gas",
            TCS: "IT Services",
            INFY: "IT Services",
            HDFCBANK: "Private Bank",
            ICICIBANK: "Private Bank",
            BHARTIARTL: "Telecom Services",
            ITC: "Tobacco & FMCG",
            SBIN: "Public Bank",
            LT: "Engineering & Construction",
            HCLTECH: "IT Services",
            MARUTI: "Auto Manufacturer",
            BAJFINANCE: "NBFC",
            ASIANPAINT: "Paints & Coatings",
            NESTLEIND: "Food Products",
            KOTAKBANK: "Private Bank",
            WOCKPHARMA: "Pharmaceuticals",
        };
        return industryMap[ticker.toUpperCase()] || "Others";
    }
    /**
     * Start the server
     */
    async start() {
        try {
            // Load instruments first
            await this.loadInstruments();
            // Start HTTP server
            this.server.listen(this.port, () => {
                LoggerService_1.logger.info("Server started", { port: this.port });
            });
            // Connect to market feed
            await this.connectToMarketFeed();
            // Start health monitoring
            this.startHealthMonitoring();
            LoggerService_1.logger.info("Dhan Market Feed Server started successfully", {
                port: this.port,
                instruments: this.instruments.length,
                subscriptionType: this.subscriptionType,
            });
        }
        catch (error) {
            LoggerService_1.logger.error("Failed to start server", {
                error: error.message,
            });
            throw error;
        }
    }
    /**
     * Start health monitoring
     */
    startHealthMonitoring() {
        setInterval(async () => {
            try {
                // Check database connection
                const dbHealthy = await DataService_1.dataService.checkConnection();
                // Log health status
                LoggerService_1.logger.debug("Health check", {
                    websocket: this.isConnected,
                    database: dbHealthy,
                    instruments: this.instruments.length,
                    liveData: this.liveData.size,
                    messages: this.messageCount,
                    memory: process.memoryUsage().heapUsed / 1024 / 1024, // MB
                });
                this.lastHealthCheck = Date.now();
            }
            catch (error) {
                LoggerService_1.logger.error("Health check failed", {
                    error: error.message,
                });
            }
        }, 30000); // Every 30 seconds
    }
    /**
     * Graceful shutdown
     */
    async shutdown() {
        LoggerService_1.logger.info("Shutting down server...");
        try {
            // Close WebSocket connection
            if (this.ws) {
                this.ws.close();
            }
            // Close Socket.IO server
            this.io.close();
            // Close HTTP server
            this.server.close();
            // Close database connections
            await DataService_1.dataService.close();
            // Cleanup cache
            CacheService_1.cacheService.destroy();
            LoggerService_1.logger.info("Server shutdown complete");
        }
        catch (error) {
            LoggerService_1.logger.error("Error during shutdown", {
                error: error.message,
            });
        }
    }
    /**
     * Get server status
     */
    getStatus() {
        return {
            connected: this.isConnected,
            instruments: this.instruments.length,
            messages: this.messageCount,
            uptime: process.uptime(),
            memory: process.memoryUsage().heapUsed / 1024 / 1024,
        };
    }
}
exports.DhanMarketFeedServer = DhanMarketFeedServer;
//# sourceMappingURL=MarketFeedServer.js.map