{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "e5a5e28cfc178b7e6d13a2e2d0bf62e3", "previewModeSigningKey": "b6e4013a2676ef33ad5607d1e4fe808f8625ba09f3a9abcdf6478d0b72e33622", "previewModeEncryptionKey": "749ecc469bd81ba63efcf25e88b876d8cde49fb709b2d225bbda4c6673362755"}}