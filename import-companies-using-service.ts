import * as xlsx from 'xlsx';
import { Pool } from 'pg';
import { config as dotenvConfig } from 'dotenv';
import path from 'path';

// Load environment variables
dotenvConfig();

// Parse PostgreSQL URL
function parsePostgresConfig() {
  const postgresUrl = process.env.POSTGRES_DATABASE_URL;
  
  if (postgresUrl) {
    const url = new URL(postgresUrl);
    return {
      host: url.hostname,
      port: parseInt(url.port) || 5432,
      database: url.pathname.slice(1),
      user: url.username,
      password: url.password,
      ssl: { rejectUnauthorized: false }
    };
  }
  
  // Fallback to individual environment variables
  return {
    host: process.env.DB_HOST || "localhost",
    port: parseInt(process.env.DB_PORT || "5432"),
    database: process.env.DB_NAME || "dhan_websocket",
    user: process.env.DB_USER || "postgres",
    password: process.env.DB_PASSWORD || "password",
    ssl: false,
  };
}

interface CompanyData {
  COMPANY_NAME: string;
  'ISIN No': string;
  Instrument: string;
  'Sector Name': string;
  'Industry New Name': string;
  'Sub-Sector': string;
  'Micro Category': string;
  BSE_SECURITY_ID: number | string;
  NSE_SECURITY_ID: number | string;
  SYMBOL: string;
}

async function main() {
  const config = parsePostgresConfig();
  const pool = new Pool(config);

  try {
    console.log('🚀 Starting company list import process...');
    
    // Read Excel file
    const filePath = path.join(__dirname, 'Companylist.xlsx');
    console.log('📖 Reading Excel file:', filePath);
    
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const companies: CompanyData[] = xlsx.utils.sheet_to_json(worksheet);
    
    console.log(`📊 Found ${companies.length} companies in Excel file`);
    
    // Connect to database
    console.log('🔗 Connecting to database...');
    const client = await pool.connect();
    console.log('✅ Connected to database');
    
    try {
      // Create table
      console.log('🏗️ Creating company_list table...');
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS company_list (
          id SERIAL PRIMARY KEY,
          company_name VARCHAR(500) NOT NULL,
          isin_no VARCHAR(20),
          instrument VARCHAR(50),
          sector_name VARCHAR(200),
          industry_new_name VARCHAR(200),
          sub_sector VARCHAR(200),
          micro_category VARCHAR(200),
          bse_security_id VARCHAR(20),
          nse_security_id VARCHAR(20),
          symbol VARCHAR(50),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `;
      
      await client.query(createTableQuery);
      console.log('✅ Table created successfully');
      
      // Clear existing data
      console.log('🗑️ Clearing existing data...');
      await client.query('DELETE FROM company_list');
      
      // Insert data in batches
      console.log('💾 Inserting company data...');
      const batchSize = 100;
      let successCount = 0;
      let errorCount = 0;
      
      for (let i = 0; i < companies.length; i += batchSize) {
        const batch = companies.slice(i, i + batchSize);
        
        for (const company of batch) {
          try {
            const nseSecurityId = company.NSE_SECURITY_ID === '-' || !company.NSE_SECURITY_ID ? null : company.NSE_SECURITY_ID.toString();
            const bseSecurityId = company.BSE_SECURITY_ID ? company.BSE_SECURITY_ID.toString() : null;

            await client.query(`
              INSERT INTO company_list (
                company_name, isin_no, instrument, sector_name, industry_new_name,
                sub_sector, micro_category, bse_security_id, nse_security_id, symbol
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            `, [
              company.COMPANY_NAME || '',
              company['ISIN No'] || '',
              company.Instrument || '',
              company['Sector Name'] || '',
              company['Industry New Name'] || '',
              company['Sub-Sector'] || '',
              company['Micro Category'] || '',
              bseSecurityId,
              nseSecurityId,
              company.SYMBOL || ''
            ]);
            successCount++;
          } catch (error) {
            console.error(`❌ Error inserting ${company.COMPANY_NAME}:`, (error as Error).message);
            errorCount++;
          }
        }
        
        console.log(`📈 Processed ${Math.min(i + batchSize, companies.length)} / ${companies.length} companies`);
      }
      
      // Show results
      const countResult = await client.query('SELECT COUNT(*) FROM company_list');
      console.log(`✅ Successfully inserted: ${successCount} companies`);
      console.log(`❌ Errors: ${errorCount} companies`);
      console.log(`📊 Total companies in database: ${countResult.rows[0].count}`);
      
      // Show sample data
      const sampleResult = await client.query('SELECT * FROM company_list LIMIT 5');
      console.log('\n📋 Sample data from database:');
      console.table(sampleResult.rows);
      
      // Show sector statistics
      const sectorStats = await client.query(`
        SELECT sector_name, COUNT(*) as count 
        FROM company_list 
        WHERE sector_name IS NOT NULL AND sector_name != ''
        GROUP BY sector_name 
        ORDER BY count DESC 
        LIMIT 10
      `);
      console.log('\n📊 Top 10 sectors by company count:');
      console.table(sectorStats.rows);
      
      console.log('🎉 Import process completed successfully!');
      
    } finally {
      client.release();
    }
    
  } catch (error) {
    console.error('❌ Import process failed:', (error as Error).message);
    console.error('Full error:', error);
  } finally {
    await pool.end();
  }
}

main();
