"use client";

import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo,
} from "react";
import { io } from "socket.io-client";
import EnhancedMarketDataTable from "../components/EnhancedMarketDataTable";
import Pagination from "../components/Pagination";
import Stats from "../components/Stats";
import ConnectionStatus from "../components/ConnectionStatus";

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

interface Stats {
  instrumentCount: number;
  updateRate: number;
  latency: number;
  lastUpdate: string;
}

export default function Home() {
  const [socket, setSocket] = useState<any>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [marketData, setMarketData] = useState<Map<string, MarketData>>(
    new Map()
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedExchange, setSelectedExchange] = useState("");

  // Pagination and sorting states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<keyof MarketData>("ticker");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [stats, setStats] = useState<Stats>({
    instrumentCount: 0,
    updateRate: 0,
    latency: 0,
    lastUpdate: "Never",
  });

  const updateCountRef = useRef(0);
  const lastSecondRef = useRef(Date.now());
  const updatesThisSecondRef = useRef(0);
  const batchUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Helper function to update stats
  const updateStats = (latency: number) => {
    const now = Date.now();
    updateCountRef.current++;
    updatesThisSecondRef.current++;

    if (now - lastSecondRef.current >= 1000) {
      setStats((prev) => ({
        ...prev,
        updateRate: updatesThisSecondRef.current,
        latency,
        lastUpdate: new Date().toLocaleTimeString(),
      }));
      updatesThisSecondRef.current = 0;
      lastSecondRef.current = now;
    }
  };

  // Optimized helper function to update single market data
  const updateMarketDataSingle = useCallback((data: MarketData) => {
    const now = Date.now();
    const latency = now - data.timestamp;
    updateStats(latency);

    setMarketData((prev) => {
      // Only update if data has actually changed
      const existing = prev.get(data.securityId);
      if (existing && existing.timestamp >= data.timestamp) {
        return prev; // Skip if we already have newer data
      }

      const newData = new Map(prev);
      newData.set(data.securityId, data);
      return newData;
    });
  }, []);

  // Optimized helper function to update batch market data
  const updateMarketDataBatch = useCallback((batch: MarketData[]) => {
    if (batch.length === 0) return;

    const now = Date.now();
    const latency = now - batch[0].timestamp;
    updateStats(latency);

    setMarketData((prev) => {
      const newData = new Map(prev);
      let hasChanges = false;

      batch.forEach((data) => {
        const existing = newData.get(data.securityId);
        if (!existing || existing.timestamp < data.timestamp) {
          newData.set(data.securityId, data);
          hasChanges = true;
        }
      });

      return hasChanges ? newData : prev; // Only return new map if there are changes
    });
  }, []);

  // Sorting function
  const handleSort = useCallback(
    (field: keyof MarketData) => {
      if (sortField === field) {
        setSortDirection(sortDirection === "asc" ? "desc" : "asc");
      } else {
        setSortField(field);
        setSortDirection("asc");
      }
      setCurrentPage(1); // Reset to first page when sorting
    },
    [sortField, sortDirection]
  );

  // Memoized filter and sort function for better performance
  const processedData = useMemo(() => {
    let filtered = Array.from(marketData.values());

    // Apply search filter
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          item.ticker.toLowerCase().includes(search) ||
          item.securityId.includes(search)
      );
    }

    // Apply exchange filter
    if (selectedExchange) {
      filtered = filtered.filter((item) => item.exchange === selectedExchange);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (typeof aValue === "string" && typeof bValue === "string") {
        const comparison = aValue.localeCompare(bValue);
        return sortDirection === "asc" ? comparison : -comparison;
      }

      if (typeof aValue === "number" && typeof bValue === "number") {
        const comparison = aValue - bValue;
        return sortDirection === "asc" ? comparison : -comparison;
      }

      return 0;
    });

    return filtered;
  }, [marketData, searchTerm, selectedExchange, sortField, sortDirection]);

  // Memoized pagination
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return processedData.slice(startIndex, endIndex);
  }, [processedData, currentPage, itemsPerPage]);

  // Calculate total pages
  const totalPages = Math.ceil(processedData.length / itemsPerPage);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedExchange, itemsPerPage]);

  useEffect(() => {
    // Connect to the WebSocket server running on port 8080
    const socketInstance = io("http://localhost:8080", {
      transports: ["websocket", "polling"],
      upgrade: true,
      rememberUpgrade: false,
      timeout: 20000,
      forceNew: true,
    });

    socketInstance.on(
      "initialData",
      (data: { instruments: any[]; liveData: MarketData[] }) => {
        setStats((prev) => ({
          ...prev,
          instrumentCount: data.instruments.length,
        }));

        // Use functional update to avoid dependency on marketData
        setMarketData((prev) => {
          const newMarketData = new Map(prev);
          data.liveData.forEach((item: MarketData) => {
            newMarketData.set(item.securityId, item);
          });
          return newMarketData;
        });
      }
    );

    // Handle individual market data updates (legacy support)
    socketInstance.on("marketData", (data: MarketData) => {
      updateMarketDataSingle(data);
    });

    // Handle batch market data updates (optimized)
    socketInstance.on("marketDataBatch", (batch: MarketData[]) => {
      updateMarketDataBatch(batch);
    });

    // Handle connection status
    socketInstance.on("connect", () => {
      console.log("✅ Connected to WebSocket server");
      setIsConnected(true);
    });

    socketInstance.on("disconnect", () => {
      console.log("❌ Disconnected from WebSocket server");
      setIsConnected(false);
    });

    socketInstance.on("connect_error", (error) => {
      console.error("❌ Connection error:", error);
      setIsConnected(false);
    });

    setSocket(socketInstance);

    // Fetch initial stats from the WebSocket server
    fetch("http://localhost:8080/api/data")
      .then((response) => response.json())
      .then((data) => {
        setStats((prev) => ({
          ...prev,
          instrumentCount: data.instruments?.length || 0,
        }));
      })
      .catch((error) => console.error("Error fetching stats:", error));

    return () => {
      socketInstance.disconnect();
    };
  }, []);

  return (
    <main className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            ⚡ Ultra-Fast Market Dashboard
          </h1>
          <ConnectionStatus isConnected={isConnected} />
        </div>

        <Stats stats={stats} />

        {/* Search and Filter Controls */}
        <div className="bg-white rounded-lg shadow p-4 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <label
                htmlFor="search"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Search Instruments
              </label>
              <input
                type="text"
                id="search"
                placeholder="Search by ticker or security ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="md:w-48">
              <label
                htmlFor="exchange"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Exchange
              </label>
              <select
                id="exchange"
                value={selectedExchange}
                onChange={(e) => setSelectedExchange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Exchanges</option>
                <option value="NSE_EQ">NSE Equity</option>
                <option value="NSE_FNO">NSE F&O</option>
                <option value="BSE_EQ">BSE Equity</option>
                <option value="MCX_COMM">MCX Commodity</option>
              </select>
            </div>
            <div className="md:w-32 flex items-end">
              <button
                type="button"
                onClick={() => {
                  setSearchTerm("");
                  setSelectedExchange("");
                }}
                className="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Clear
              </button>
            </div>
          </div>
          <div className="mt-2 text-sm text-gray-600">
            Showing {paginatedData.length} of {processedData.length} instruments
            {processedData.length !== marketData.size && (
              <span className="text-gray-500">
                {" "}
                (filtered from {marketData.size} total)
              </span>
            )}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow overflow-hidden">
          <EnhancedMarketDataTable
            data={paginatedData}
            sortField={sortField}
            sortDirection={sortDirection}
            onSort={handleSort}
          />
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={setItemsPerPage}
            totalItems={processedData.length}
          />
        </div>
      </div>
    </main>
  );
}
